# Subdomain Documentation Index

Complete documentation for the multi-subdomain architecture in this application.

## Documentation Overview

This application uses a sophisticated subdomain-based architecture to serve multiple branded versions from a single codebase. The documentation is organized into the following sections:

### 📚 Main Documentation

1. **[Subdomain Architecture](./SUBDOMAIN-ARCHITECTURE.md)** (Comprehensive Guide)
   - Complete explanation of how subdomains work
   - Architecture components and design decisions
   - Development workflow and best practices
   - SSR considerations and troubleshooting
   - **Read this first** for a complete understanding

2. **[Quick Reference Guide](./SUBDOMAIN-QUICK-REFERENCE.md)** (Developer Cheatsheet)
   - Quick lookup for common tasks
   - Code snippets and examples
   - Configuration reference
   - Troubleshooting table
   - **Use this** for day-to-day development

3. **[Flow Diagrams](./SUBDOMAIN-FLOW-DIAGRAMS.md)** (Visual Reference)
   - Visual representations of subdomain flows
   - Request processing diagrams
   - Component loading decision trees
   - Architecture illustrations
   - **Refer to this** for understanding the big picture

## Quick Start

### For New Developers

If you're new to this codebase and need to understand subdomain handling:

1. **Read**: [Subdomain Architecture](./SUBDOMAIN-ARCHITECTURE.md) - Sections 1-3
2. **Visualize**: [Flow Diagrams](./SUBDOMAIN-FLOW-DIAGRAMS.md) - Request Flow & Detection Logic
3. **Reference**: [Quick Reference](./SUBDOMAIN-QUICK-REFERENCE.md) - Keep this open while coding

### For Experienced Developers

If you need to work with subdomains quickly:

1. **Bookmark**: [Quick Reference Guide](./SUBDOMAIN-QUICK-REFERENCE.md)
2. **Review**: [Common Tasks](./SUBDOMAIN-ARCHITECTURE.md#common-patterns)
3. **Debug**: [Troubleshooting Section](./SUBDOMAIN-ARCHITECTURE.md#troubleshooting)

## Current Subdomains

| Subdomain | Brand | URL (Production) | Primary Use |
|-----------|-------|------------------|-------------|
| `star` | Star Team | `star.housepriceguess.com` | Team-based gaming |
| `bvh` | Buena Vista Homes | `bvh.housepriceguess.com` | Real estate branding |
| `costa` | Costa | `costa.housepriceguess.com` | Regional focus |
| (default) | HousePriceGuess | `housepriceguess.com` | Main product |

## Key Concepts at a Glance

### Entry Points
- **`hpg-main`**: House Price Guess with subdomain support (star, bvh, costa)
- **`htoc-subdomains`**: HomesToCompare focused subdomains
- **`psq-subdomains`**: PropertySquares gaming subdomains

### Detection Mechanism
1. Boot file extracts subdomain from hostname
2. Stored globally for SSR/CSR compatibility
3. Router uses utility functions for conditional loading
4. Components receive subdomain through props/inject

### File Organization
```
src/
├── apps/                    # Entry points (hpg-main, htoc-subdomains, psq-subdomains)
├── boot/
│   └── pwb-flex-conf.js    # Subdomain detection
├── concerns/
│   ├── bvh/                # BVH-specific code
│   ├── costa/              # Costa-specific code
│   ├── hpg/                # HousePriceGuess (default + star)
│   └── branded-game/       # Shared branded components
└── compose/                # Shared composables
```

## Common Tasks

### Testing Locally
```bash
ROOT_FOLDER_NAME=hpg-main quasar dev

# Then visit:
# http://star.lvh.me:9000
# http://bvh.lvh.me:9000
# http://costa.lvh.me:9000
```

### Accessing Subdomain in Code
```javascript
// In Vue components
import { inject } from 'vue'
const pwbFlexConfig = inject('pwbFlexConfig')
console.log(pwbFlexConfig.subdomainName)

// In router/guards
import { whichBrandSubdomain } from 'src/apps/hpg-main/router/subdomain-utils'
const brand = whichBrandSubdomain()
```

### Conditional Component Loading
```javascript
// In routes.js
component: () =>
  isBvhSubdomain()
    ? import('src/concerns/bvh/layouts/BvhLayout.vue')
    : import('src/concerns/hpg/layouts/HpgLayout.vue')
```

## Architecture Highlights

### ✅ Strengths
- **Single Codebase**: One repository serves multiple brands
- **SSR Compatible**: Works with server-side rendering
- **Dynamic Loading**: Components load on-demand per subdomain
- **Type-Safe**: Utility functions prevent subdomain detection errors
- **Scalable**: Easy to add new subdomains

### 🔧 Key Implementation Details
- **Early Boot Detection**: Subdomain detected before router initialization
- **Global State**: Stored in `globalThis` for SSR/CSR compatibility
- **Lazy Loading**: Brand-specific components only load when needed
- **Environment-Aware**: Different API bases for dev/prod

### 📋 Best Practices
1. Always use utility functions (`isBvhSubdomain()`) instead of direct string checks
2. Access subdomain through `pwbFlexConfig` or `globalThis` for SSR safety
3. Keep brand-specific code in `src/concerns/{brand}/` folders
4. Test with `lvh.me` subdomains during development
5. Verify boot file order in `quasar.config.js`

## Troubleshooting Guide

| Issue | Quick Fix | Documentation |
|-------|-----------|---------------|
| Subdomain not detected | Use `lvh.me` not `localhost` | [Quick Ref](./SUBDOMAIN-QUICK-REFERENCE.md#troubleshooting) |
| Wrong layout loads | Check boot file order | [Architecture](./SUBDOMAIN-ARCHITECTURE.md#troubleshooting) |
| SSR hydration error | Use `globalThis` access | [Architecture](./SUBDOMAIN-ARCHITECTURE.md#ssr-considerations) |
| API calls fail | Verify `dataApiBase` | [Quick Ref](./SUBDOMAIN-QUICK-REFERENCE.md#api-calls-with-subdomain) |

## Development Workflow

### Adding a New Subdomain Brand

1. **Create brand folder**: `src/concerns/{brand}/`
2. **Create layout**: `{brand}/layouts/{Brand}Layout.vue`
3. **Create pages**: `{brand}/pages/{Brand}HomePage2025.vue`
4. **Add utilities**: Update `subdomain-utils.js`
5. **Update routes**: Add to route conditions
6. **Test locally**: `http://{brand}.lvh.me:9000`

📖 **Detailed guide**: [Subdomain Architecture - Common Tasks](./SUBDOMAIN-ARCHITECTURE.md#common-patterns)

### Working with Existing Subdomains

1. **Find brand code**: Check `src/concerns/{brand}/`
2. **Update components**: Edit brand-specific files
3. **Test changes**: Use `lvh.me` subdomains
4. **Build**: Use appropriate build script

📖 **Code examples**: [Quick Reference - Code Snippets](./SUBDOMAIN-QUICK-REFERENCE.md#code-snippets)

## Related Documentation

### Application-Wide Docs
- [Main README](../README.md) - Project overview and setup
- [SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md) - Server-side rendering details
- [JSON-LD Implementation](./JSON-LD-IMPLEMENTATION.md) - SEO structured data
- [Testing Checklist](./testing-checklist.md) - QA procedures

### External Resources
- [Quasar Boot Files](https://quasar.dev/quasar-cli-vite/boot-files)
- [Vue Router Dynamic Imports](https://router.vuejs.org/guide/advanced/lazy-loading.html)
- [lvh.me Documentation](http://lvh.me/) - Local subdomain testing

## Contributing

When working with subdomains:

1. **Maintain SSR compatibility**: Always test with `quasar dev -m ssr`
2. **Document new subdomains**: Update all three documentation files
3. **Follow folder structure**: Keep brand code in `src/concerns/{brand}/`
4. **Use utilities**: Never hardcode subdomain checks
5. **Test all variants**: Verify changes work across all subdomains

## Questions?

- **Technical details**: See [Subdomain Architecture](./SUBDOMAIN-ARCHITECTURE.md)
- **Quick answers**: Check [Quick Reference](./SUBDOMAIN-QUICK-REFERENCE.md)
- **Visual understanding**: Review [Flow Diagrams](./SUBDOMAIN-FLOW-DIAGRAMS.md)
- **General setup**: Refer to [Main README](../README.md)

---

**Last Updated**: October 2025
**Maintained By**: Development Team
**Status**: Active - Supports star, bvh, costa subdomains
