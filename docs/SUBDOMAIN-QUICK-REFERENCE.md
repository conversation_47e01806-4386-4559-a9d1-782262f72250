# Subdomain Quick Reference

Quick reference guide for working with subdomains in this application.

## Supported Subdomains

| Subdomain | Brand | Entry Point | Primary Use Case |
|-----------|-------|-------------|------------------|
| `star` | Star Team | `hpg-main` | Gaming/team features |
| `bvh` | Buena Vista Homes | `hpg-main` | Real estate agency branding |
| `costa` | Costa | `hpg-main` | Regional property focus |
| `(none)` | HousePriceGuess | `hpg-main` | Default gaming experience |

## Quick Setup

### 1. Start Dev Server with Subdomain Support

```bash
# Terminal
ROOT_FOLDER_NAME=hpg-main quasar dev
```

### 2. Test Different Subdomains Locally

```bash
# Open in browser:
http://star.lvh.me:9000      # Star Team
http://bvh.lvh.me:9000       # Buena Vista Homes
http://costa.lvh.me:9000     # Costa
http://lvh.me:9000           # Default
```

## Code Snippets

### Detect Current Subdomain

```javascript
// Method 1: Using inject (in Vue components)
import { inject } from 'vue'
const pwbFlexConfig = inject('pwbFlexConfig')
console.log(pwbFlexConfig.subdomainName) // 'bvh', 'costa', 'star', etc.

// Method 2: Using utility functions (in router/guards)
import { whichBrandSubdomain } from 'src/apps/hpg-main/router/subdomain-utils'
const brand = whichBrandSubdomain() // 'bvh', 'costa', 'star', or ''

// Method 3: Direct global access (SSR-safe)
const subdomain = globalThis.__SUBDOMAIN_NAME__
```

### Check Specific Subdomain

```javascript
import {
  isStarSubdomain,
  isBvhSubdomain,
  isCostaSubdomain
} from 'src/apps/hpg-main/router/subdomain-utils'

if (isStarSubdomain()) {
  // Star Team specific logic
}

if (isBvhSubdomain()) {
  // BVH specific logic
}

if (isCostaSubdomain()) {
  // Costa specific logic
}
```

### Conditional Component Loading in Routes

```javascript
{
  path: '/',
  component: () =>
    isStarSubdomain()
      ? import('src/concerns/hpg/layouts/StarTeamLayout.vue')
      : isBvhSubdomain()
      ? import('src/concerns/bvh/layouts/BvhLayout.vue')
      : isCostaSubdomain()
      ? import('src/concerns/costa/layouts/CostaLayout.vue')
      : import('src/concerns/hpg/layouts/HpgLayout.vue'),
}
```

### Conditional Rendering in Templates

```vue
<template>
  <div>
    <!-- Show different content based on subdomain -->
    <BvhBanner v-if="isBvh" />
    <CostaBanner v-else-if="isCosta" />
    <DefaultBanner v-else />
  </div>
</template>

<script setup>
import { inject, computed } from 'vue'

const pwbFlexConfig = inject('pwbFlexConfig')
const isBvh = computed(() => pwbFlexConfig.subdomainName === 'bvh')
const isCosta = computed(() => pwbFlexConfig.subdomainName === 'costa')
</script>
```

### API Calls with Subdomain

```javascript
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

// API base is automatically set based on subdomain
const response = await axios.get(
  `${pwbFlexConfig.dataApiBase}/api/games`
)

// pwbFlexConfig.dataApiBase examples:
// - Production: https://bvh.homestocompare.com
// - Development: http://bvh.lvh.me:3333
```

## File Structure

### Where to Add Brand-Specific Code

```
src/concerns/
├── bvh/                      # BVH-specific code
│   ├── layouts/
│   │   └── BvhLayout.vue     # BVH layout
│   ├── pages/
│   │   └── BvhHomePage2025.vue
│   └── components/
│       └── BvhSpecificComponent.vue
│
├── costa/                    # Costa-specific code
│   ├── layouts/
│   │   └── CostaLayout.vue
│   └── pages/
│       └── CostaHomePage2025.vue
│
├── hpg/                      # Default & Star Team
│   ├── layouts/
│   │   ├── HpgLayout.vue     # Default
│   │   └── StarTeamLayout.vue # Star Team
│   └── pages/
│       ├── HpgHomePage2025.vue
│       └── StarTeamHomePage2025.vue
│
└── branded-game/             # Shared between all brands
    └── components/
```

## Configuration Files

### Environment Variables

```bash
# .env.local.hpg-main-2025-june-prod
ROOT_FOLDER_NAME="hpg-main"
PRODUCT_NAME="House Price Guess"
G_TAG="G-KV7J2EP44W"

# .env.local.pwbprofeh2c-2025-apr-prod
ROOT_FOLDER_NAME="htoc-subdomains"
PRODUCT_NAME="HomesToCompare"
G_TAG="G-4TWSYP29D5"

# .env.local.propsqrs-sbd-2025-june-prod
ROOT_FOLDER_NAME="psq-subdomains"
PRODUCT_NAME="PropertySquares"
G_TAG="G-DFVLR8219Y"
```

### Boot Order (quasar.config.js)

```javascript
boot: [
  'axios',              // 1. HTTP client
  'pwb-flex-conf',      // 2. Subdomain detection (MUST BE EARLY)
  'calendar',
  // ... other boot files
]
```

## Common Tasks

### Add a New Subdomain Brand

1. **Create brand folder**: `src/concerns/newbrand/`
2. **Add layout**: `src/concerns/newbrand/layouts/NewBrandLayout.vue`
3. **Add homepage**: `src/concerns/newbrand/pages/NewBrandHomePage2025.vue`
4. **Update utilities**: `src/apps/hpg-main/router/subdomain-utils.js`
   ```javascript
   export function isNewBrandSubdomain(hostname) {
     return getSubdomainFromHostname(hostname) === 'newbrand'
   }
   ```
5. **Update routes**: `src/apps/hpg-main/router/routes.js`
   ```javascript
   component: () =>
     isStarSubdomain()
       ? import('src/concerns/hpg/layouts/StarTeamLayout.vue')
       : isNewBrandSubdomain()
       ? import('src/concerns/newbrand/layouts/NewBrandLayout.vue')
       : // ... other brands
   ```

### Debug Subdomain Detection

```javascript
// In any component or boot file
console.log('Current subdomain:', pwbFlexConfig.subdomainName)
console.log('API base:', pwbFlexConfig.dataApiBase)
console.log('Site base URL:', pwbFlexConfig.siteBaseUrl)
console.log('Global subdomain:', globalThis.__SUBDOMAIN_NAME__)
```

### Test SSR with Subdomains

```bash
# Start SSR dev mode
ROOT_FOLDER_NAME=hpg-main quasar dev -m ssr

# Test in browser
http://bvh.lvh.me:9000
```

## Troubleshooting

| Issue | Solution |
|-------|----------|
| Subdomain not detected | Use `lvh.me` instead of `localhost` |
| Wrong layout loads | Check boot file order in `quasar.config.js` |
| API calls fail | Verify `pwbFlexConfig.dataApiBase` is set |
| SSR hydration mismatch | Ensure utilities use `globalThis` for SSR safety |
| Vite blocks subdomain | Check `allowedHosts` in `quasar.config.js` |

## Build Commands

```bash
# Development
ROOT_FOLDER_NAME=hpg-main quasar dev

# Production build for specific entry point
ROOT_FOLDER_NAME=hpg-main quasar build
ROOT_FOLDER_NAME=htoc-subdomains quasar build
ROOT_FOLDER_NAME=psq-subdomains quasar build

# Or use deployment scripts
./deploy/dokkubuild/build-prod-hpg-main-2025-june.sh
./deploy/dokkubuild/build-prod-pwbprofeh2c-2025-apr.sh
./deploy/dokkubuild/build-prod-propsqrs-sbd-2025-june.sh
```

## Key Concepts

- **Entry Point** (`ROOT_FOLDER_NAME`): Determines which app variant to build (hpg-main, htoc-subdomains, psq-subdomains)
- **Subdomain Detection**: Happens at boot time, stored globally for SSR safety
- **Routing**: Dynamic imports based on subdomain check functions
- **API Base**: Automatically configured per subdomain
- **Concerns**: Organized by brand/feature for maintainability

## Related Documentation

- [Full Subdomain Architecture Documentation](./SUBDOMAIN-ARCHITECTURE.md)
- [SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md)
- [JSON-LD Implementation](./JSON-LD-IMPLEMENTATION.md)
