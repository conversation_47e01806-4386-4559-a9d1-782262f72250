# New Subdomain Configuration Architecture

## Visual Overview

```
┌─────────────────────────────────────────────────────────────────┐
│           NEW: Centralized Configuration System                  │
│                  (October 2025)                                  │
└─────────────────────────────────────────────────────────────────┘

┌──────────────────────────────────────────────────────────────────┐
│  src/apps/hpg-main/router/subdomain-config.js                    │
│  ┌────────────────────────────────────────────────────────────┐  │
│  │  SUBDOMAIN_BRANDS = {                                      │  │
│  │    star: { name, displayName, logoUrl, components... },   │  │
│  │    bvh: { ... },                                           │  │
│  │    cameron: { ... },     ← Simply add here!               │  │
│  │    costa: { ... }                                          │  │
│  │  }                                                         │  │
│  └────────────────────────────────────────────────────────────┘  │
│                                                                   │
│  Single source of truth for all subdomain configurations         │
└──────────────────────────────────────────────────────────────────┘
                              │
                              ▼
        ┌─────────────────────────────────────────┐
        │  Utilities & Loaders                    │
        │  (automatically use config)             │
        └─────────────────────────────────────────┘
                │                    │
                ▼                    ▼
    ┌──────────────────┐    ┌──────────────────┐
    │ subdomain-utils  │    │ subdomain-loader │
    │                  │    │                  │
    │ • getBrandConfig │    │ • getLayout()    │
    │ • isValidBrand   │    │ • getHomePage()  │
    │ • whichBrand     │    │                  │
    └──────────────────┘    └──────────────────┘
                │                    │
                └────────┬───────────┘
                         │
                         ▼
              ┌────────────────────┐
              │   routes.js        │
              │   (simplified!)    │
              │                    │
              │ component: () =>   │
              │   getSubdomain     │
              │   Layout()         │
              └────────────────────┘
                         │
                         ▼
              ┌────────────────────┐
              │  Dynamic Import    │
              │  Based on Config   │
              └────────────────────┘
                         │
                         ▼
        ┌────────────────────────────────────┐
        │  Loads Correct Brand Components    │
        │                                    │
        │  src/concerns/cameron/             │
        │  src/concerns/bvh/                 │
        │  src/concerns/costa/               │
        │  src/concerns/hpg/                 │
        └────────────────────────────────────┘
```

## Request Flow with New System

```
User visits: cameron.housepriceguess.com
                    │
                    ▼
         ┌──────────────────────┐
         │ Subdomain Detection  │
         │ (boot/pwb-flex-conf) │
         └──────────────────────┘
                    │
                    ▼ subdomainName = 'cameron'
         ┌──────────────────────┐
         │ Router Initialization│
         └──────────────────────┘
                    │
                    ▼
         ┌──────────────────────────────┐
         │ getSubdomainLayout() called  │
         └──────────────────────────────┘
                    │
                    ▼
         ┌───────────────────────────────────┐
         │ getBrandConfig('cameron')         │
         │ Returns:                          │
         │   layoutComponent: 'src/concerns/ │
         │   cameron/layouts/CameronLayout'  │
         └───────────────────────────────────┘
                    │
                    ▼
         ┌──────────────────────────────────┐
         │ import('src/concerns/cameron/    │
         │ layouts/CameronLayout.vue')      │
         └──────────────────────────────────┘
                    │
                    ▼
         ┌──────────────────────────────────┐
         │ Cameron Layout Rendered          │
         │ with Cameron branding            │
         └──────────────────────────────────┘
```

## Before vs After Comparison

### BEFORE (Manual Configuration)

```javascript
// routes.js - Had to update multiple places
const routes = [
  {
    path: '/',
    component: () =>
      isStarSubdomain()
        ? import('src/concerns/hpg/layouts/StarTeamLayout.vue')
        : isBvhSubdomain()
        ? import('src/concerns/bvh/layouts/BvhLayout.vue')
        : isCostaSubdomain()
        ? import('src/concerns/costa/layouts/CostaLayout.vue')
        : import('src/concerns/hpg/layouts/HpgLayout.vue'),
    children: [
      {
        component: () =>
          isStarSubdomain()
            ? import('src/concerns/hpg/pages/StarTeamHomePage2025.vue')
            : isBvhSubdomain()
            ? import('src/concerns/bvh/pages/BvhHomePage2025.vue')
            : isCostaSubdomain()
            ? import('src/concerns/costa/pages/CostaHomePage2025.vue')
            : import('src/concerns/hpg/pages/HpgHomePage2025.vue'),
      }
    ]
  },
  // ... more routes with same pattern repeated
]

// subdomain-utils.js
export function isBvhSubdomain(hostname) {
  return getSubdomainFromHostname(hostname) === 'bvh'
}
export function isCostaSubdomain(hostname) {
  return getSubdomainFromHostname(hostname) === 'costa'
}
// ... one function per subdomain

❌ Problems:
- Repetitive code
- Multiple places to update
- Easy to miss locations
- Hard to maintain
```

### AFTER (Configuration-Driven)

```javascript
// subdomain-config.js - SINGLE place to add subdomain
export const SUBDOMAIN_BRANDS = {
  cameron: {
    name: 'cameron',
    displayName: 'Cameron Properties',
    layoutComponent: 'src/concerns/cameron/layouts/CameronLayout.vue',
    homePageComponent: 'src/concerns/cameron/pages/CameronHomePage2025.vue',
    // ... other config
  }
}

// routes.js - Simple and clean
const routes = [
  {
    path: '/',
    component: () => getSubdomainLayout(),  // ← Automatic!
    children: [
      {
        component: () => getSubdomainHomePage(),  // ← Automatic!
      }
    ]
  }
]

// subdomain-utils.js - Generic functions
export function isBrandSubdomain(brandName, hostname) {
  return getSubdomainFromHostname(hostname) === brandName
}

✅ Benefits:
- Single source of truth
- DRY principle
- Easy to maintain
- Scalable
```

## Adding a New Subdomain: Step-by-Step

```
┌─────────────────────────────────────────────────────────────────┐
│                    1. Update Configuration                       │
│  Edit: src/apps/hpg-main/router/subdomain-config.js            │
│                                                                  │
│  export const SUBDOMAIN_BRANDS = {                              │
│    // ... existing brands ...                                   │
│    newbrand: {                ← Add this object                 │
│      name: 'newbrand',                                          │
│      layoutComponent: 'src/concerns/newbrand/layouts/...',      │
│      homePageComponent: 'src/concerns/newbrand/pages/...',      │
│      // ... other properties                                    │
│    }                                                            │
│  }                                                              │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    2. Create Brand Folder                        │
│  mkdir -p src/concerns/newbrand/layouts                         │
│  mkdir -p src/concerns/newbrand/pages                           │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    3. Copy Template Files                        │
│  cp src/concerns/cameron/layouts/CameronLayout.vue \           │
│     src/concerns/newbrand/layouts/NewBrandLayout.vue           │
│                                                                  │
│  cp src/concerns/cameron/pages/CameronHomePage2025.vue \       │
│     src/concerns/newbrand/pages/NewBrandHomePage2025.vue       │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    4. Customize Components                       │
│  • Update component names                                        │
│  • Change branding (logo, text, colors)                         │
│  • Update game slug                                             │
│  • Customize content                                            │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    5. Test Locally                              │
│  quasar dev                                                      │
│  → Visit: http://newbrand.lvh.me:9000                          │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
                         ✅ DONE!
```

## Configuration Properties Map

```
SUBDOMAIN_BRANDS.cameron = {

  name ─────────────────────► 'cameron'
                               │
                               └─► Used for subdomain detection
                                   cameron.housepriceguess.com

  displayName ──────────────► 'Cameron Properties'
                               │
                               └─► Shown in UI, headers, titles

  homeUrl ──────────────────► 'https://cameron.housepriceguess.com/'
                               │
                               └─► Used in links, navigation

  serviceEmail ─────────────► '<EMAIL>'
                               │
                               └─► Contact info in footer/forms

  logoUrl ──────────────────► '/icons/favicon-128x128.png'
                               │
                               └─► Header logo image

  layoutComponent ──────────► 'src/concerns/cameron/layouts/CameronLayout.vue'
                               │
                               └─► Dynamic import path for layout

  homePageComponent ────────► 'src/concerns/cameron/pages/CameronHomePage2025.vue'
                               │
                               └─► Dynamic import path for homepage

  brandedGameSlug ──────────► 'arizona-house-price-guess'
                               │
                               └─► API identifier for game data

  description ──────────────► 'Cameron Properties branded experience'
                               │
                               └─► Used in documentation/metadata
}
```

## Migration Path

```
┌─────────────────────────────────────────────────────────────────┐
│                 Legacy Code (Still Works!)                       │
├─────────────────────────────────────────────────────────────────┤
│  if (isBvhSubdomain()) { ... }          ✅ Still supported       │
│  if (isCostaSubdomain()) { ... }        ✅ Still supported       │
│  if (isCameronSubdomain()) { ... }      ✅ Still supported       │
└─────────────────────────────────────────────────────────────────┘
                              │
                              │ Can migrate gradually
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                 Modern Code (Recommended)                        │
├─────────────────────────────────────────────────────────────────┤
│  if (isBrandSubdomain('bvh')) { ... }   ✅ Generic function      │
│  const config = getCurrentBrandConfig() ✅ Full configuration    │
│  const brand = whichBrandSubdomain()    ✅ Returns brand name    │
└─────────────────────────────────────────────────────────────────┘
```

## Summary

### What Changed

✅ **Added**: Central configuration file (`subdomain-config.js`)
✅ **Added**: Dynamic component loader (`subdomain-loader.js`)
✅ **Added**: Cameron subdomain with layout and homepage
✅ **Updated**: Utilities to use configuration
✅ **Simplified**: Router code (no more long conditionals)
✅ **Maintained**: 100% backward compatibility

### Key Benefits

- 🚀 **Easy to Add**: New subdomains in minutes, not hours
- 🎯 **Single Source**: One file to update
- 🔧 **Maintainable**: Clear and organized
- 📈 **Scalable**: Add unlimited subdomains
- 🔄 **Flexible**: Can extend to load from API
- ✅ **Compatible**: All existing code still works

### Next Subdomain?

Just add to `subdomain-config.js` and create the components. That's it!

---

**Date**: October 4, 2025
**Status**: Implemented and Ready
