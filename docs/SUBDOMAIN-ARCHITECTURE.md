# Subdomain Architecture Documentation

## Overview

This application uses a multi-subdomain architecture where different subdomains serve different brands or application variants while sharing the same codebase. The subdomain determines which layout, components, and configuration are loaded at runtime.

## Supported Subdomains

The application currently supports the following branded subdomains:

1. **`star`** - Star Team branded version
2. **`bvh`** - Buena Vista Homes branded version
3. **`costa`** - Costa branded version
4. **Default** - Main HousePriceGuess version (when no special subdomain is detected)

## Architecture Components

### 1. Application Entry Points (`ROOT_FOLDER_NAME`)

The application has three main entry points defined by the `ROOT_FOLDER_NAME` environment variable:

- **`hpg-main`** - House Price Guess main application (default)
  - Location: `src/apps/hpg-main/`
  - Includes subdomain detection utilities and branded routing

- **`htoc-subdomains`** - HomesToCompare subdomains
  - Location: `src/apps/htoc-subdomains/`
  - For property comparison focused subdomains

- **`psq-subdomains`** - PropertySquares subdomains
  - Location: `src/apps/psq-subdomains/`
  - For property game focused subdomains

Configuration in `quasar.config.js`:
```javascript
const ROOT_FOLDER_NAME = process.env.ROOT_FOLDER_NAME || 'hpg-main'

sourceFiles: {
  rootComponent: `src/apps/${ROOT_FOLDER_NAME}/App.vue`,
  router: `src/apps/${ROOT_FOLDER_NAME}/router/index`,
}
```

### 2. Subdomain Detection

Subdomain detection happens in two places:

#### A. Boot Configuration (`src/boot/pwb-flex-conf.js`)

This boot file runs during application initialization and detects the subdomain from:
- **Server-side (SSR)**: `ssrContext.req.headers.host`
- **Client-side (CSR)**: `window.location.hostname`

The subdomain is extracted using this logic:
```javascript
function stripPort(host) {
  if (!host) return ''
  return host.split(':')[0]
}

// Extract subdomain from hostname
const hostname = stripPort(window.location.hostname) // or from SSR context
const parts = hostname.split('.')
const isIpHost = /^\d+(?:\.\d+){3}$/.test(hostname)

if (!isIpHost && parts.length > 2) {
  subdomainName = parts[0] // e.g., 'bvh' from 'bvh.housepriceguess.com'
}
```

The detected subdomain is then:
1. Set as a global property: `app.config.globalProperties.$subdomainName`
2. Stored in `pwbFlexConfig` object
3. Exposed globally: `globalThis.__SUBDOMAIN_NAME__` and `globalThis.__PWB_FLEX_CONFIG__`
4. Provided to the Vue app: `app.provide('pwbFlexConfig', pwbFlexConfig)`

#### B. Router Utilities (`src/apps/hpg-main/router/subdomain-utils.js`)

Utility functions for safe subdomain detection in both browser and SSR environments:

```javascript
// Main detection function
export function getSubdomainFromHostname(hostname) {
  // Priority:
  // 1) Explicit hostname passed in
  // 2) Global set by boot (server or client)
  // 3) window.location.hostname (client only)
  // 4) Fallback to empty string
}

// Brand-specific checks
export function isStarSubdomain(hostname)
export function isBvhSubdomain(hostname)
export function isCostaSubdomain(hostname)
export function whichBrandSubdomain(hostname)
```

These utilities ensure subdomain detection works correctly in:
- SSR pre-fetch
- Client-side hydration
- Dynamic imports
- Router guards

### 3. Routing Configuration

Routes dynamically load different components based on subdomain detection.

Example from `src/apps/hpg-main/router/routes.js`:

```javascript
import { isStarSubdomain, isBvhSubdomain, isCostaSubdomain } from './subdomain-utils'

const routes = [
  {
    path: '/',
    // Different layout for each subdomain
    component: () =>
      isStarSubdomain()
        ? import('src/concerns/hpg/layouts/StarTeamLayout.vue')
        : isBvhSubdomain()
        ? import('src/concerns/bvh/layouts/BvhLayout.vue')
        : isCostaSubdomain()
        ? import('src/concerns/costa/layouts/CostaLayout.vue')
        : import('src/concerns/hpg/layouts/HpgLayout.vue'),
    children: [
      {
        path: '',
        name: 'rSubdomainRoot',
        // Different homepage for each subdomain
        component: () =>
          isStarSubdomain()
            ? import('src/concerns/hpg/pages/StarTeamHomePage2025.vue')
            : isBvhSubdomain()
            ? import('src/concerns/bvh/pages/BvhHomePage2025.vue')
            : isCostaSubdomain()
            ? import('src/concerns/costa/pages/CostaHomePage2025.vue')
            : import('src/concerns/hpg/pages/HpgHomePage2025.vue'),
      },
      // ... more routes
    ]
  }
]
```

### 4. Brand-Specific Concerns

Each branded subdomain has its own folder in `src/concerns/`:

```
src/concerns/
├── bvh/                    # Buena Vista Homes specific code
│   ├── layouts/
│   │   └── BvhLayout.vue
│   └── pages/
│       └── BvhHomePage2025.vue
├── costa/                  # Costa specific code
│   ├── layouts/
│   │   └── CostaLayout.vue
│   └── pages/
│       └── CostaHomePage2025.vue
├── hpg/                    # House Price Guess (default & star)
│   ├── layouts/
│   │   ├── HpgLayout.vue
│   │   └── StarTeamLayout.vue
│   └── pages/
│       ├── HpgHomePage2025.vue
│       └── StarTeamHomePage2025.vue
└── branded-game/           # Shared branded components
    └── components/
```

#### Layout Components

Each branded layout includes:
- Custom header with brand logo
- Brand-specific colors and styling
- Custom footer
- Brand-specific service email and display name

Example from `BvhLayout.vue`:
```vue
<template>
  <q-layout view="lhh LpR ffr" class="hpg-main-layout-outer">
    <q-header class="hpg-main-mht-ctr bg-white" reveal elevated>
      <q-toolbar>
        <q-toolbar-title>
          <a href="https://bvh.housepriceguess.com/">
            <img
              src="https://www.buenavistahomes.eu/packs/media/images/buenavista-logo-header-542fb3999ff7b04a4dd1c745a5f4b4cf.svg"
              alt="Buena Vista Homes Logo"
            />
          </a>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <!-- Router view with branded props -->
    <q-page-container>
      <router-view
        :whitelabelNameDisplay="whitelabelNameDisplay"
        :serviceEmail="serviceEmail"
        <!-- ... other props -->
      />
    </q-page-container>
  </q-layout>
</template>
```

### 5. API Configuration

The subdomain also determines the API base URL:

```javascript
// From boot/pwb-flex-conf.js
let prodDataApiBase = `https://${subdomainName}.homestocompare.com`
let devDataApiBase = `http://${subdomainName}.lvh.me:3333`

let dataApiBase = process.env.PROD ? prodDataApiBase : devDataApiBase

pwbFlexConfig = {
  dataApiBase: dataApiBase,
  subdomainName: subdomainName,
  siteBaseUrl: siteBaseUrl,
}
```

This means:
- **Production**: Each subdomain connects to its own backend at `{subdomain}.homestocompare.com`
- **Development**: Uses local development server at `{subdomain}.lvh.me:3333`

## Development Workflow

### 1. Local Development with Subdomains

To test subdomain behavior locally, use `lvh.me` (a DNS service that resolves to `127.0.0.1`):

```bash
# Start dev server
ROOT_FOLDER_NAME=hpg-main quasar dev

# Access different subdomains:
# http://star.lvh.me:9000       - Star Team version
# http://bvh.lvh.me:9000        - BVH version
# http://costa.lvh.me:9000      - Costa version
# http://lvh.me:9000            - Default version
```

The `quasar.config.js` is configured to allow `lvh.me` hosts:
```javascript
viteConf.server = viteConf.server || {}
viteConf.server.allowedHosts = ['lvh.me', '.lvh.me']
```

### 2. Environment Configuration

Different environments use different `.env` files:

- `.env.local.hpg-main-2025-june-prod` - HousePriceGuess production
  ```bash
  ROOT_FOLDER_NAME="hpg-main"
  PRODUCT_NAME="House Price Guess"
  G_TAG="G-KV7J2EP44W"
  ```

- `.env.local.pwbprofeh2c-2025-apr-prod` - HomesToCompare production
  ```bash
  ROOT_FOLDER_NAME="htoc-subdomains"
  PRODUCT_NAME="HomesToCompare"
  G_TAG="G-4TWSYP29D5"
  ```

- `.env.local.propsqrs-sbd-2025-june-prod` - PropertySquares production
  ```bash
  ROOT_FOLDER_NAME="psq-subdomains"
  PRODUCT_NAME="PropertySquares"
  G_TAG="G-DFVLR8219Y"
  ```

### 3. Build and Deployment

Each variant has its own build script:

```bash
# From package.json
"dev": "ROOT_FOLDER_NAME=hpg-main quasar dev"
"dev:ssr": "ROOT_FOLDER_NAME=hpg-main quasar dev -m ssr"

# From deploy/dokkubuild/
./build-prod-hpg-main-2025-june.sh       # Builds hpg-main
./build-prod-pwbprofeh2c-2025-apr.sh     # Builds htoc-subdomains
./build-prod-propsqrs-sbd-2025-june.sh   # Builds psq-subdomains
```

## Common Patterns

### 1. Accessing Subdomain in Components

```javascript
// Using inject
import { inject } from 'vue'
const pwbFlexConfig = inject('pwbFlexConfig')
console.log(pwbFlexConfig.subdomainName)

// Using globalThis (SSR-safe)
const subdomain = globalThis.__SUBDOMAIN_NAME__

// Using utility functions
import { whichBrandSubdomain } from 'src/apps/hpg-main/router/subdomain-utils'
const brand = whichBrandSubdomain()
```

### 2. Conditional Rendering Based on Subdomain

```vue
<template>
  <div>
    <BrandedHeader v-if="isStarBrand" />
    <DefaultHeader v-else />
  </div>
</template>

<script setup>
import { computed, inject } from 'vue'
const pwbFlexConfig = inject('pwbFlexConfig')
const isStarBrand = computed(() => pwbFlexConfig.subdomainName === 'star')
</script>
```

### 3. API Calls with Subdomain Context

```javascript
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

async function fetchGameData() {
  const response = await axios.get(
    `${pwbFlexConfig.dataApiBase}/api/games`,
    {
      headers: {
        'X-Subdomain': pwbFlexConfig.subdomainName
      }
    }
  )
  return response.data
}
```

## SSR Considerations

The subdomain architecture is designed to work with Server-Side Rendering (SSR):

1. **Server-side detection**: Subdomain is detected from request headers
2. **Global exposure**: Subdomain is stored in `globalThis` for SSR-safe access
3. **SSR context**: `ssrContext` is passed through the boot process
4. **Hydration safety**: Client-side picks up the same subdomain value

Example SSR flow:
```
1. Request arrives at server: bvh.housepriceguess.com
2. Boot file detects: subdomainName = 'bvh'
3. Router resolves: BvhLayout and BvhHomePage components
4. Server renders HTML with BVH branding
5. Client hydrates with same subdomain detection
6. Router matches same components, no flash
```

## Troubleshooting

### Issue: Subdomain not detected in development

**Solution**: Use `lvh.me` instead of `localhost`:
```
# Instead of: http://localhost:9000
# Use: http://subdomain.lvh.me:9000
```

### Issue: Wrong layout loads after navigation

**Cause**: Dynamic imports may be caching the wrong component.

**Solution**: Ensure subdomain utilities don't rely on client-only APIs. Use the provided utilities from `subdomain-utils.js` which have proper SSR support.

### Issue: API calls failing with subdomain mismatch

**Cause**: `pwbFlexConfig` not properly initialized before API calls.

**Solution**: Ensure boot files run in correct order in `quasar.config.js`:
```javascript
boot: [
  'axios',           // First
  'pwb-flex-conf',   // Second - sets up subdomain
  // ... other boot files
]
```

## Future Enhancements

Potential improvements to the subdomain architecture:

1. **Dynamic subdomain registration**: Allow new subdomains without code changes
2. **Subdomain configuration API**: Store brand configs in database
3. **Multi-region support**: Different subdomains for different geographic regions
4. **A/B testing**: Route percentage of traffic to experimental layouts
5. **White-label portal**: Admin UI for partners to customize their subdomain

## Related Documentation

- [SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md)
- [JSON-LD Implementation](./JSON-LD-IMPLEMENTATION.md)
- [Testing Checklist](./testing-checklist.md)
