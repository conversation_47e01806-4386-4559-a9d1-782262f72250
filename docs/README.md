# Documentation Index

Welcome to the project documentation. This folder contains comprehensive guides for understanding and working with the application.

## 📖 Documentation Sections

### Core Documentation

- **[Subdomain Architecture](./SUBDOMAIN-INDEX.md)** ⭐ NEW
  - Complete guide to multi-subdomain architecture
  - How subdomains like `bvh`, `costa`, and `star` are handled
  - Includes quick reference, flow diagrams, and troubleshooting
  - **Start here** if working with branded versions

- **[SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md)**
  - Server-side rendering data prefetch patterns
  - Component lifecycle in SSR mode
  - Best practices for data loading

- **[JSON-LD Implementation](./JSON-LD-IMPLEMENTATION.md)**
  - Structured data for SEO
  - Schema.org integration
  - Search engine optimization strategies

- **[Testing Checklist](./testing-checklist.md)**
  - QA procedures and test scenarios
  - Manual testing guidelines
  - Quality assurance workflow

- **[GL Title Attribute Implementation](./gl_title_atr-implementation.md)**
  - Google Analytics title attribute handling
  - Analytics implementation details

### Feature-Specific Documentation

#### Realty Game
- [Currency Defaults](./realty-game/currency-defaults.md)
  - Multi-currency support in property games
  - Default currency configuration by region

#### LLM-Generated Content
- [SEO Action Plan](./from-llms/seo-actionplan.md)
  - AI-assisted SEO strategy
  - Content optimization recommendations

## 🚀 Quick Navigation

### By Role

**New Developer**
1. [Main README](../README.md) - Start here
2. [Subdomain Architecture](./SUBDOMAIN-INDEX.md) - Understand multi-brand setup
3. [Testing Checklist](./testing-checklist.md) - QA procedures

**Frontend Developer**
1. [Subdomain Architecture](./SUBDOMAIN-INDEX.md) - Brand-specific components
2. [SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md) - Data loading patterns
3. [JSON-LD Implementation](./JSON-LD-IMPLEMENTATION.md) - SEO implementation

**QA Engineer**
1. [Testing Checklist](./testing-checklist.md) - Test procedures
2. [Subdomain Quick Reference](./SUBDOMAIN-QUICK-REFERENCE.md) - Testing different brands

### By Task

**Setting up development environment**
→ [Main README](../README.md) + [Subdomain Quick Reference](./SUBDOMAIN-QUICK-REFERENCE.md)

**Adding a new branded subdomain**
→ [Subdomain Architecture - Common Tasks](./SUBDOMAIN-ARCHITECTURE.md#common-patterns)

**Implementing SEO features**
→ [JSON-LD Implementation](./JSON-LD-IMPLEMENTATION.md)

**Understanding data loading**
→ [SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md)

**Testing the application**
→ [Testing Checklist](./testing-checklist.md)

## 🎯 Most Frequently Accessed

Based on developer needs, these are the most commonly referenced docs:

1. **[Subdomain Quick Reference](./SUBDOMAIN-QUICK-REFERENCE.md)** - Daily development
2. **[Testing Checklist](./testing-checklist.md)** - Before deployment
3. **[SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md)** - Component development
4. **[Subdomain Flow Diagrams](./SUBDOMAIN-FLOW-DIAGRAMS.md)** - Visual understanding

## 📂 File Structure

```
docs/
├── README.md (this file)
│
├── SUBDOMAIN-INDEX.md              # Multi-subdomain overview
├── SUBDOMAIN-ARCHITECTURE.md       # Comprehensive subdomain guide
├── SUBDOMAIN-QUICK-REFERENCE.md    # Developer cheatsheet
├── SUBDOMAIN-FLOW-DIAGRAMS.md      # Visual diagrams
│
├── SSR-PREFETCH-FLOW.md            # Server-side rendering
├── JSON-LD-IMPLEMENTATION.md       # SEO structured data
├── testing-checklist.md            # QA procedures
├── gl_title_atr-implementation.md  # Analytics implementation
│
├── realty-game/
│   └── currency-defaults.md        # Game currency config
│
└── from-llms/
    └── seo-actionplan.md           # AI SEO recommendations
```

## 🔍 Finding What You Need

### Search by Keyword

- **Subdomain, brand, multi-tenant**: [Subdomain Index](./SUBDOMAIN-INDEX.md)
- **bvh, costa, star**: [Subdomain Architecture](./SUBDOMAIN-ARCHITECTURE.md)
- **SSR, server-side, prefetch**: [SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md)
- **SEO, meta tags, structured data**: [JSON-LD Implementation](./JSON-LD-IMPLEMENTATION.md)
- **Testing, QA, checklist**: [Testing Checklist](./testing-checklist.md)
- **Currency, international**: [Currency Defaults](./realty-game/currency-defaults.md)

### Common Questions

**Q: How do I test different branded versions locally?**
A: See [Subdomain Quick Reference - Local Development](./SUBDOMAIN-QUICK-REFERENCE.md#quick-setup)

**Q: How do I add a new subdomain brand?**
A: See [Subdomain Architecture - Adding New Brand](./SUBDOMAIN-ARCHITECTURE.md#common-patterns)

**Q: Why isn't my component loading data?**
A: Check [SSR Prefetch Flow](./SSR-PREFETCH-FLOW.md)

**Q: How do I implement SEO for a new page?**
A: Review [JSON-LD Implementation](./JSON-LD-IMPLEMENTATION.md)

**Q: What should I test before deployment?**
A: Follow [Testing Checklist](./testing-checklist.md)

## 🎨 Documentation Standards

When contributing to documentation:

1. **Use Markdown**: All docs should be `.md` files
2. **Include diagrams**: Use ASCII art or link to images
3. **Add code examples**: Show real, working code snippets
4. **Keep it current**: Update docs when code changes
5. **Link related docs**: Cross-reference relevant documentation
6. **Add to this index**: Update this README when adding new docs

## 📝 Contributing

To add or update documentation:

1. Create/edit markdown file in appropriate folder
2. Add entry to this README
3. Cross-reference from related docs
4. Include practical examples
5. Test all code snippets
6. Update last-modified date

## 🔗 External Resources

### Quasar Framework
- [Quasar Documentation](https://quasar.dev/)
- [Quasar Boot Files](https://quasar.dev/quasar-cli-vite/boot-files)
- [Quasar SSR Mode](https://quasar.dev/quasar-cli-vite/developing-ssr/introduction)

### Vue.js
- [Vue 3 Documentation](https://vuejs.org/)
- [Vue Router](https://router.vuejs.org/)
- [Pinia (State Management)](https://pinia.vuejs.org/)

### Development Tools
- [Vite](https://vitejs.dev/)
- [Axios](https://axios-http.com/)
- [Playwright Testing](https://playwright.dev/)

## 📅 Last Updated

**Date**: October 4, 2025
**Major Changes**: Added comprehensive subdomain architecture documentation

---

**Need help?** If you can't find what you're looking for, check:
1. The main [project README](../README.md)
2. Code comments in relevant files
3. Git history for context on changes
