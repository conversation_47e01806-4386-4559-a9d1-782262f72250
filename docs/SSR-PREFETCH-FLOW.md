# SSR and PreFetch Flow Documentation

## Overview

This document explains the Server-Side Rendering (SSR) and data prefetching flow for the roundup game routes in the application, specifically for URLs like:

```
http://localhost:9100/roundup/v/fabulous-uk
```

## The HTML Parsing Error

### Problem Description

When visiting roundup game URLs, you may encounter this error:

```
Unable to parse HTML; parse5 error code missing-whitespace-between-attributes
```

### Root Cause

The error occurs when game data containing HTML content (like `game_description`) is serialized into the `window.__INITIAL_STATE__` object during SSR. The issue is specifically with how **double quotes within HTML attributes** are handled when embedded in the server-rendered HTML.

#### Example Problem Data

```json
{
  "game_description": "<section class=\"quirky-homes-intro\">\n  <div>\n    We've rounded up the quirkiest properties—think avocado bathrooms...\n  </div>\n  <div class=\"source-note\">\n    <a class=\"text-body1 text-white\" href=\"https://www.reddit.com/r/SpottedOnRightmove/\" target=\"_blank\" rel=\"noopener noreferrer\">\n      r/SpottedOnRightmove on Reddit\n    </a>\n  </div>\n</section>\n"
}
```

When this gets serialized into:
```html
<script>window.__INITIAL_STATE__={"realtyGame":{"gameDesc":"<section class=\"quirky-homes-intro\">..."}}</script>
```

The unescaped quotes in the HTML string break the parse5 HTML parser because they create malformed HTML attributes in the script tag context.

### Solution

The application uses **sanitization** to remove control characters and problematic Unicode sequences, but it **does NOT escape quotes** in HTML strings. This is intentional - the proper fix is to ensure the SSR serialization process properly escapes the JSON when embedding it in HTML.

## Route Configuration

### Route Definition

File: `src/apps/hpg-main/router/routes.roundup.js`

```javascript
{
  path: 'roundup/v/:gameSlug',
  name: 'rRoundupGame',
  component: () => import('src/concerns/realty-game/layouts/RoundupGameLayout.vue'),
  meta: {
    title: 'Property Price Challenge - Interactive Real Estate Game',
    description: 'Test your property market knowledge...',
    keywords: 'property price game, real estate challenge...',
    ogType: 'website',
    ogImage: 'https://assets.propertysquares.com/...',
    twitterCard: 'summary_large_image',
  },
  children: [
    {
      path: '',
      name: 'rRoundupGameStart',
      component: () => import('src/concerns/realty-game/pages/roundup/RoundupGameStartPage.vue'),
      // ... more config
    },
    {
      path: 'property/:listingInGameUuid',
      component: () => import('src/concerns/realty-game/layouts/RoundupGamePagesLayout.vue'),
      children: [
        {
          path: '',
          name: 'rRoundupGameProperty',
          component: () => import('src/concerns/realty-game/pages/roundup/RoundupGamePropertyPage.vue'),
          // THIS COMPONENT HAS A preFetch HOOK
        }
      ]
    }
    // ... more child routes
  ]
}
```

## SSR Flow

### 1. Request Initiation

When a user visits `http://localhost:9100/roundup/v/fabulous-uk`:

1. Express server receives the request
2. `src-ssr/middlewares/render.js` catches all routes with `app.get(resolve.urlPath('*'))`
3. Calls the Quasar `render()` function with SSR context: `{ req, res }`

### 2. Server Configuration

File: `src-ssr/server.js`

Key configurations:
- **Express app** is created with compression middleware (production only)
- **Static content serving** with 30-day cache (production)
- **Preload tag generation** for JS, CSS, fonts, and images

### 3. Boot Files Execution

File: `quasar.config.js` (boot files section)

```javascript
boot: [
  'i18n',
  'axios',
  'pwb-flex-conf',  // Loads subdomain and environment config
  'calendar',
  { server: false, path: 'apexcharts' },  // Client-only
  { server: false, path: 'ahoy' },        // Client-only
],
```

#### Important: `pwb-flex-conf.js`

File: `src/boot/pwb-flex-conf.js`

This boot file runs on **both server and client**:

```javascript
export default async ({ app, ssrContext }) => {
  let subdomainName = 'deff'
  
  if (process.env.CLIENT) {
    // Client-side: use window.location.hostname
    const hostname = window.location.hostname
    subdomainName = hostname.split('.')[0]
  } else if (process.env.SERVER && ssrContext) {
    // Server-side: use SSR context request headers
    const hostname = ssrContext.req.headers.host
    subdomainName = hostname.split('.')[0]
  }

  // Fetch subdomain environment from headers
  let subdomainEnv = ''
  if (process.env.SERVER) {
    subdomainEnv = ssrContext.req.headers['x-subdomain-env']
  } else {
    const response = await fetch(window.location.href)
    subdomainEnv = response.headers.get('X-Subdomain-Env')
  }

  // Build API configuration based on subdomain and environment
  // ... configuration logic ...
  
  // Export pwbFlexConfig globally
  app.config.globalProperties.$pwbFlexConfig = pwbFlexConfig
}
```

### 4. Component PreFetch Execution

File: `src/concerns/realty-game/pages/roundup/RoundupGamePropertyPage.vue`

When SSR encounters a component with a `preFetch` hook, it executes **on the server** before rendering:

```javascript
async preFetch({ currentRoute, ssrContext }) {
  const gameSlug = currentRoute.params.gameSlug  // e.g., 'fabulous-uk'
  const listingInGameUuid = currentRoute.params.listingInGameUuid

  const { default: axios } = await import('axios')
  const { pwbFlexConfig } = await import('boot/pwb-flex-conf')
  const { useRealtyGameStore } = await import('src/stores/realtyGame')

  // Fetch game data and property data in parallel
  const [gameResponse, propertyResponse] = await Promise.all([
    axios.get(`${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/${gameSlug}`),
    axios.get(`${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/${listingInGameUuid}`)
  ])

  // Transform and store data
  const store = useRealtyGameStore()
  
  const storeData = {
    gameListings: flattenedGameListings,
    gameTitle: realtyGameDetails?.game_title || 'Property Price Challenge',
    gameDesc: realtyGameDetails?.game_description || '',  // ⚠️ This may contain HTML with quotes
    gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
    gameDefaultCurrency: realtyGameDetails?.default_game_currency || 'ZAR',
    currentProperty: enhancedPropertyData,
    isDataLoaded: true,
  }

  store.setRealtyGameData(storeData)  // This triggers sanitization
}
```

### 5. Pinia Store Data Handling

File: `src/stores/realtyGame.js`

The store **sanitizes all incoming data** before storing:

```javascript
import { deepSanitize } from 'src/utils/sanitize'

export const useRealtyGameStore = defineStore('realtyGame', {
  state: () => ({
    gameListings: [],
    realtyGameListings: {},
    gameTitle: 'Property Price Challenge',
    gameDesc: '',
    gameBgImageUrl: '',
    gameDefaultCurrency: 'DKK',
    currentProperty: null,
    isDataLoaded: false,
  }),
  
  actions: {
    setRealtyGameData(data) {
      // ✅ SANITIZATION HAPPENS HERE
      const sanitizedData = deepSanitize(data)
      
      this.gameListings = sanitizedData.gameListings || []
      this.gameTitle = sanitizedData.gameTitle || 'Property Price Challenge'
      this.gameDesc = sanitizedData.gameDesc || ''
      this.gameBgImageUrl = sanitizedData.gameBgImageUrl || ''
      this.gameDefaultCurrency = sanitizedData.gameDefaultCurrency || 'DKK'
      this.currentProperty = sanitizedData.currentProperty || null
      this.isDataLoaded = true
    }
  }
})
```

### 6. Data Sanitization

File: `src/utils/sanitize.js`

```javascript
export function sanitizeString(value) {
  if (typeof value !== 'string') return value
  return value
    // Remove C0 control chars (keep TAB, LF, CR)
    .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F]/g, ' ')
    // Normalize Unicode line/para separators
    .replace(/[\u2028\u2029]/g, ' ')
    // Remove problematic DEL and C1 control chars
    .replace(/[\u007F-\u009F]/g, ' ')
}

export function deepSanitize(input) {
  if (Array.isArray(input)) {
    return input.map(v => deepSanitize(v))
  }
  if (input && typeof input === 'object') {
    const out = {}
    for (const [k, v] of Object.entries(input)) {
      out[k] = deepSanitize(v)
    }
    return out
  }
  return sanitizeString(input)
}
```

**⚠️ Important:** This sanitization removes control characters and Unicode line separators, but it **does NOT escape double quotes** or other characters that would break HTML/JSON serialization. This is intentional - proper escaping should happen during serialization into `window.__INITIAL_STATE__`.

### 7. State Serialization into HTML

During SSR, Quasar/Pinia automatically serializes the store state into the rendered HTML:

```html
<script>window.__INITIAL_STATE__={"realtyGame":{"gameDesc":"<section class=\"quirky-homes-intro\">..."}}</script>
```

**This is where the problem occurs**: If the serialization doesn't properly escape quotes in JSON strings, the HTML parser (parse5) will fail.

### 8. Client-Side Hydration

File: `src/stores/index.js`

On the client side:
1. Browser parses the HTML
2. JavaScript executes and reads `window.__INITIAL_STATE__`
3. Pinia store is rehydrated with the server state
4. Vue takes over and makes the app interactive

## Layout Component Flow

File: `src/concerns/realty-game/layouts/RoundupGameLayout.vue`

```vue
<template>
  <div class="roundup-game-layout">
    <div class="round-up-header">
      <!-- Header with game title and description -->
      <h1>{{ storeGameTitle }}</h1>
      <div v-html="storeGameDescription"></div>  <!-- ⚠️ Renders HTML from store -->
    </div>
    
    <div v-if="gameTitle" class="game-content">
      <router-view
        :game-session-id="routeSssnId"
        :gameTitle="gameTitle"
        :gameDefaultCurrency="gameDefaultCurrency"
        @update-progress="handleProgressUpdate"
        @game-complete="handleGameComplete"
      />
    </div>
  </div>
</template>

<script setup>
import { useRealtyGame } from 'src/concerns/realty-game/composables/useRealtyGame'
import { useRealtyGameStore } from 'src/stores/realtyGame'

const {
  totalProperties,
  gameTitle,
  fetchPriceGuessData,
  gameDesc,
  gameDefaultCurrency,
} = useRealtyGame()

const realtyGameStore = useRealtyGameStore()
const storeGameTitle = computed(() => gameTitle.value)
const storeGameDescription = computed(() => gameDesc.value)

const initializeGame = async () => {
  await fetchPriceGuessData($route.params.gameSlug)
}

onMounted(() => {
  initializeGame()
})
</script>
```

## Data Flow Summary

```
1. User visits /roundup/v/fabulous-uk
   ↓
2. Express server (src-ssr/server.js) receives request
   ↓
3. Quasar SSR render middleware (src-ssr/middlewares/render.js)
   ↓
4. Boot files execute:
   - pwb-flex-conf.js (determines API base URL)
   - Other boot files
   ↓
5. Router matches route and loads RoundupGameLayout
   ↓
6. Child route loads RoundupGamePropertyPage (if property page)
   ↓
7. preFetch hook executes ON SERVER:
   - Fetches game data from API
   - Fetches property data from API
   - Stores in Pinia with sanitization
   ↓
8. Component renders with prefetched data
   ↓
9. Pinia state serialized to window.__INITIAL_STATE__
   ⚠️ If HTML contains unescaped quotes → parse5 error
   ↓
10. HTML sent to browser
   ↓
11. Browser parses HTML
   ↓
12. Client-side JS hydrates from window.__INITIAL_STATE__
   ↓
13. Vue takes over, app becomes interactive
```

## API Endpoints Called

For `http://localhost:9100/roundup/v/fabulous-uk`:

1. **Game Summary**
   ```
   GET ${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/fabulous-uk
   ```
   Returns:
   - `price_guess_inputs.game_listings[]` - Array of game properties
   - `realty_game_details.game_title` - "Quirky Homes Roundup"
   - `realty_game_details.game_description` - HTML description with embedded HTML tags
   - `realty_game_details.game_bg_image_url` - Background image URL
   - `realty_game_details.default_game_currency` - "GBP"

2. **Property Details** (when visiting a property page)
   ```
   GET ${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/{listingInGameUuid}
   ```
   Returns:
   - `sale_listing` - Full property details
   - Property images, descriptions, pricing, etc.

## Fixes and Recommendations

### Current Issue: HTML Parsing Error

The `game_description` field contains HTML with double quotes in attributes:
```html
<a class="text-body1 text-white" href="..." target="_blank" rel="noopener noreferrer">
```

When serialized into `window.__INITIAL_STATE__`, these quotes can break the HTML parser.

### Recommended Solutions

1. **Server-Side: Properly Escape JSON in HTML Context**
   - Ensure Quasar's SSR serialization properly escapes quotes when embedding JSON in `<script>` tags
   - The JSON string should escape `"` as `\"` or use HTML entities

2. **Backend: Send Plain Text Instead of HTML**
   - Store game descriptions as markdown or plain text
   - Render HTML on the frontend using a safe markdown parser
   - This prevents any HTML injection or parsing issues

3. **Frontend: HTML Entity Encoding**
   - Before storing HTML strings in the Pinia store, encode HTML entities:
   ```javascript
   function encodeHtmlEntities(str) {
     return str
       .replace(/"/g, '&quot;')
       .replace(/'/g, '&#39;')
       .replace(/</g, '&lt;')
       .replace(/>/g, '&gt;')
   }
   ```

4. **Use JSON.stringify Properly**
   - When manually creating the `__INITIAL_STATE__` script, use:
   ```javascript
   const stateJson = JSON.stringify(state)
     .replace(/</g, '\\u003c')
     .replace(/>/g, '\\u003e')
     .replace(/&/g, '\\u0026')
   ```

### Current Workaround

The `deepSanitize` function removes control characters but doesn't fix the quote escaping issue. You may need to:

1. Check if Quasar has configuration for SSR state serialization
2. Look at how `window.__INITIAL_STATE__` is generated
3. Add proper escaping at that serialization point

## Key Files Reference

- **Route Config**: `src/apps/hpg-main/router/routes.roundup.js`
- **SSR Server**: `src-ssr/server.js`
- **SSR Middleware**: `src-ssr/middlewares/render.js`
- **Boot Config**: `src/boot/pwb-flex-conf.js`
- **Layout**: `src/concerns/realty-game/layouts/RoundupGameLayout.vue`
- **Property Page**: `src/concerns/realty-game/pages/roundup/RoundupGamePropertyPage.vue`
- **Store**: `src/stores/realtyGame.js`
- **Sanitizer**: `src/utils/sanitize.js`
- **Store Setup**: `src/stores/index.js`

## Debugging Tips

1. **Check SSR logs**: Look for preFetch console.log statements
2. **Inspect HTML source**: View page source and look for `window.__INITIAL_STATE__`
3. **Test API responses**: Call the API endpoints directly to see the raw data
4. **Validate JSON**: Copy the `__INITIAL_STATE__` value and validate it with a JSON validator
5. **Disable SSR temporarily**: Test if the issue only occurs during SSR

## Environment Variables

The SSR flow uses these environment variables:
- `process.env.SERVER` - True when running on server
- `process.env.CLIENT` - True when running on client
- `process.env.PROD` - True in production mode
- `process.env.DEV` - True in development mode
