# Subdomain Configuration Guide

## Overview

As of October 2025, this application uses a **centralized configuration system** for managing branded subdomains. This makes it much easier to add, modify, or remove subdomains without touching multiple files.

## Quick Start: Adding a New Subdomain

To add a new subdomain (like we did with "cameron"), follow these steps:

### 1. Add Configuration Entry

Edit `src/apps/hpg-main/router/subdomain-config.js` and add your new subdomain to the `SUBDOMAIN_BRANDS` object:

```javascript
export const SUBDOMAIN_BRANDS = {
  // ... existing brands ...

  yournew: {
    name: 'yournew',
    displayName: 'Your New Brand',
    homeUrl: 'https://yournew.housepriceguess.com/',
    serviceEmail: '<EMAIL>',
    whitelabelName: 'HousePriceGuess',
    logoUrl: '/icons/favicon-128x128.png', // or your logo URL
    layoutComponent: 'src/concerns/yournew/layouts/YourNewLayout.vue',
    homePageComponent: 'src/concerns/yournew/pages/YourNewHomePage2025.vue',
    brandedGameSlug: 'yournew-house-prices-game',
    description: 'Your brand description'
  }
}
```

### 2. Create Brand Folder Structure

```bash
mkdir -p src/concerns/yournew/layouts
mkdir -p src/concerns/yournew/pages
```

### 3. Copy Template Files

The easiest way is to copy from an existing brand like BVH or Cameron:

```bash
# Copy layout
cp src/concerns/cameron/layouts/CameronLayout.vue \
   src/concerns/yournew/layouts/YourNewLayout.vue

# Copy homepage
cp src/concerns/cameron/pages/CameronHomePage2025.vue \
   src/concerns/yournew/pages/YourNewHomePage2025.vue
```

### 4. Customize the Files

Update the copied files:
- Change component name in `export default defineComponent({ name: 'YourNewLayout' })`
- Update logo URL and home URL
- Update brand name references
- Customize branded game slug

### 5. Test Locally

```bash
# Start dev server
ROOT_FOLDER_NAME=hpg-main quasar dev

# Visit in browser
http://yournew.lvh.me:9000
```

That's it! The router automatically loads components based on the configuration.

## Configuration Properties Explained

Each subdomain entry in `SUBDOMAIN_BRANDS` has these properties:

| Property | Type | Description | Example |
|----------|------|-------------|---------|
| `name` | string | Subdomain identifier (must match subdomain) | `'cameron'` |
| `displayName` | string | Human-readable brand name | `'Cameron Properties'` |
| `homeUrl` | string | Brand's home URL | `'https://cameron.housepriceguess.com/'` |
| `serviceEmail` | string | Support/contact email | `'<EMAIL>'` |
| `whitelabelName` | string | White-label display name | `'HousePriceGuess'` |
| `logoUrl` | string | Path or URL to brand logo | `'/icons/favicon-128x128.png'` |
| `layoutComponent` | string | Path to layout component | `'src/concerns/cameron/layouts/CameronLayout.vue'` |
| `homePageComponent` | string | Path to homepage component | `'src/concerns/cameron/pages/CameronHomePage2025.vue'` |
| `brandedGameSlug` | string | Game identifier for API | `'arizona-house-price-guess'` |
| `description` | string | Brief description | `'Cameron Properties branded experience'` |

## How It Works

### Configuration-Driven Routing

The router now uses dynamic component loading based on configuration:

```javascript
// OLD WAY (before October 2025) - Hard-coded conditions
component: () =>
  isStarSubdomain()
    ? import('src/concerns/hpg/layouts/StarTeamLayout.vue')
    : isBvhSubdomain()
    ? import('src/concerns/bvh/layouts/BvhLayout.vue')
    : // ... more conditions

// NEW WAY (after October 2025) - Configuration-driven
component: () => getSubdomainLayout()
```

The `getSubdomainLayout()` function:
1. Detects current subdomain
2. Looks up configuration in `SUBDOMAIN_BRANDS`
3. Returns the appropriate component dynamically

### Utility Functions

#### Configuration Access

```javascript
import { getBrandConfig, isValidBrand, getAllBrandNames } from './subdomain-config'

// Get config for specific brand
const cameronConfig = getBrandConfig('cameron')
console.log(cameronConfig.displayName) // 'Cameron Properties'

// Check if brand exists
const isValid = isValidBrand('cameron') // true

// Get all registered brands
const brands = getAllBrandNames() // ['star', 'bvh', 'cameron', 'costa']
```

#### Component Loading

```javascript
import { getSubdomainLayout, getSubdomainHomePage } from './subdomain-loader'

// Get layout for current subdomain
const layout = await getSubdomainLayout()

// Get homepage for current subdomain
const homepage = await getSubdomainHomePage()
```

#### Subdomain Detection

```javascript
import {
  isCameronSubdomain,
  isBrandSubdomain,
  getCurrentBrandConfig
} from './subdomain-utils'

// Legacy: Specific brand check
if (isCameronSubdomain()) {
  // Cameron-specific logic
}

// New: Generic brand check
if (isBrandSubdomain('cameron')) {
  // Cameron-specific logic
}

// Best: Get full config
const config = getCurrentBrandConfig()
console.log(config.brandedGameSlug) // 'arizona-house-price-guess'
```

## File Structure

```
src/apps/hpg-main/router/
├── subdomain-config.js          # ⭐ Central configuration
├── subdomain-utils.js           # Detection utilities
├── subdomain-loader.js          # Dynamic component loading
├── routes.js                    # Main routes (uses config)
└── index.js                     # Router setup

src/concerns/
├── cameron/                     # Cameron brand files
│   ├── layouts/
│   │   └── CameronLayout.vue
│   └── pages/
│       └── CameronHomePage2025.vue
├── bvh/                        # BVH brand files
│   ├── layouts/
│   │   └── BvhLayout.vue
│   └── pages/
│       └── BvhHomePage2025.vue
└── [other brands...]
```

## Currently Supported Subdomains

| Subdomain | Brand | Status | Config Entry |
|-----------|-------|--------|--------------|
| `star` | Star Team | ✅ Active | `SUBDOMAIN_BRANDS.star` |
| `bvh` | Buena Vista Homes | ✅ Active | `SUBDOMAIN_BRANDS.bvh` |
| `cameron` | Cameron Properties | ✅ Active | `SUBDOMAIN_BRANDS.cameron` |
| `costa` | Costa | ✅ Active | `SUBDOMAIN_BRANDS.costa` |
| (none) | Default HousePriceGuess | ✅ Active | `DEFAULT_BRAND` |

## Migration Notes

### Before October 2025 (Old System)

- Each new subdomain required updates in multiple places:
  - `subdomain-utils.js` - Add new `isBrandSubdomain()` function
  - `routes.js` - Add new conditional in multiple places
  - Manual component path management
  - Easy to miss locations and create bugs

### After October 2025 (New System)

- Single source of truth: `subdomain-config.js`
- Automatic routing based on configuration
- Easier maintenance and scaling
- Less code duplication

### Backward Compatibility

All old check functions still work:
- `isStarSubdomain()`
- `isBvhSubdomain()`
- `isCameronSubdomain()`
- `isCostaSubdomain()`

But new code should prefer:
- `isBrandSubdomain('brandname')`
- `getCurrentBrandConfig()`

## Best Practices

### DO ✅

1. **Add configuration first** before creating files
2. **Use consistent naming** (lowercase, no spaces)
3. **Test locally** with `lvh.me` subdomains
4. **Copy from existing brands** as templates
5. **Update documentation** when adding brands

### DON'T ❌

1. **Don't hardcode** subdomain checks in multiple places
2. **Don't skip** the configuration step
3. **Don't forget** to update both layout and homepage
4. **Don't use** inconsistent naming
5. **Don't deploy** without local testing

## Troubleshooting

### Issue: New subdomain not loading

**Check:**
1. Is it in `subdomain-config.js`?
2. Are file paths correct in config?
3. Do the component files exist?
4. Are you using `lvh.me` (not `localhost`)?

### Issue: Wrong component loading

**Check:**
1. Component paths in config match actual files
2. No typos in `layoutComponent` or `homePageComponent`
3. Files are exported correctly

### Issue: 404 on component import

**Check:**
1. Path in config is relative to `src/`
2. File extension is `.vue`
3. File exists at specified location

## Advanced Usage

### Custom Component Loading

If you need special logic beyond configuration:

```javascript
// In routes.js
component: () => {
  const config = getCurrentBrandConfig()

  // Special case handling
  if (config.name === 'special' && someCondition) {
    return import('src/concerns/special/SpecialLayout.vue')
  }

  // Default: use config
  return getSubdomainLayout()
}
```

### Dynamic Configuration

For truly dynamic subdomains (loaded from API):

```javascript
// Could extend to load from API
export async function loadBrandConfigFromAPI(subdomainName) {
  const response = await fetch(`/api/subdomain-config/${subdomainName}`)
  const config = await response.json()

  // Cache or store config
  SUBDOMAIN_BRANDS[subdomainName] = config

  return config
}
```

## Related Documentation

- [Subdomain Architecture](./SUBDOMAIN-ARCHITECTURE.md) - Detailed architecture
- [Subdomain Quick Reference](./SUBDOMAIN-QUICK-REFERENCE.md) - Quick commands
- [Subdomain Flow Diagrams](./SUBDOMAIN-FLOW-DIAGRAMS.md) - Visual guide

## Changelog

- **October 4, 2025**: Introduced centralized configuration system
  - Created `subdomain-config.js`
  - Updated `subdomain-utils.js` to use config
  - Created `subdomain-loader.js` for dynamic loading
  - Updated `routes.js` to use new system
  - Added "cameron" subdomain as example
  - Updated all documentation
