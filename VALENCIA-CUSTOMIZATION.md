# Valencia Subdomain Customization for <PERSON>

## Agent Information
- **Name**: <PERSON>
- **Company**: Valencia Property
- **Email**: <EMAIL>
- **Phone**: +34 ***********
- **WhatsApp**: 34657994311
- **Website**: https://www.valencia-property.com
- **Location**: Valencia, Spain
- **Experience**: 20+ years in Valencia real estate

## Google Analytics
- **ID**: UA-946302-2

## Company Story
The story of Valencia Property is simple. <PERSON> was looking to buy a house in Valencia and the service by the huge majority of agents was absolutely terrible. It took forever and they never listened to what he wanted, they just took him to see what they could offer.

The last straw was when an agent showed three houses in a wasted afternoon, none of which corresponded to what <PERSON> wanted and all of which were well above the budget and then asked: "OK, which are you buying?".

Six months later Valencia Property was born when a friend, having seen the chaos that had unfolded, asked <PERSON> to help find him a house. In six weeks the house had been found, a price agreed and a mortgage done. Valencia Property was born.

The company has now been working in Valencia for two decades and goes from strength to strength. Valencia Property has helped thousands of people find their perfect home in and around Valencia.

## What Sets Valencia Property Apart
- Highly personalized, friendly and informal service
- Extreme professionalism
- Twenty years of experience in the Valencia Property market
- Comprehensive network of lawyers, architects, surveyors, builders, painters, electricians, plumbers, tax advisors, gardeners, pool maintenance people

## Files Modified

### 1. `/src/apps/hpg-main/router/subdomain-config.js`
```javascript
valencia: {
  name: 'valencia',
  displayName: 'Graham Hunt - Valencia Property',
  homeUrl: 'https://valencia.housepriceguess.com/',
  serviceEmail: '<EMAIL>',
  whitelabelName: 'Valencia Property',
  logoUrl: '/icons/favicon-128x128.png',
  layoutComponent: 'src/concerns/valencia/layouts/ValenciaLayout.vue',
  homePageComponent: 'src/concerns/valencia/pages/ValenciaHomePage2025.vue',
  brandedGameSlug: 'valencia-house-prices-game',
  description: 'Graham Hunt - Valencia Property - Expert Real Estate Services in Valencia, Spain',
  phone: '+34 ***********',
  whatsapp: '34657994311',
  company: 'Valencia Property',
  website: 'https://www.valencia-property.com',
  googleAnalyticsId: 'UA-946302-2',
  location: 'Valencia, Spain'
}
```

### 2. `/src/concerns/valencia/layouts/ValenciaLayout.vue`
**Props to update:**
```javascript
props: {
  serviceEmail: {
    type: String,
    default: '<EMAIL>',
  },
  whitelabelNameDisplay: {
    type: String,
    default: 'Graham Hunt - Valencia Property',
  },
}
```

**Header title:**
```vue
<span class="text-h6 text-primary no-wrap">Graham Hunt - Valencia Property</span>
```

**Logo alt text:**
```vue
alt="Graham Hunt - Valencia Property"
```

### 3. `/src/concerns/valencia/pages/ValenciaHomePage2025.vue`

**⚠️ THIS FILE NEEDS MANUAL REPAIR - It's currently corrupted**

**Page Title:**
```vue
Graham Hunt's Valencia Property Price Challenge
```

**Game Slug:**
```javascript
gameSlug: 'valencia-house-prices-game'
```

**Subtitle:**
```vue
<p class="text-h6 text-primary q-mb-lg">
  🏠 Test your Valencia property market knowledge with Graham Hunt!
  Two decades of experience in Valencia real estate. 🏡
</p>
```

**First Paragraph:**
```vue
<p class="text-body1 text-grey-7">
  {{ gameDesc }}
  The story of Valencia Property is simple. Graham was looking to buy a house in Valencia and
  the service by the huge majority of agents was absolutely terrible. It took forever and they
  never listened to what he wanted. That frustration led to the birth of Valencia Property
  over two decades ago.
</p>
```

**Second Paragraph:**
```vue
<p class="text-body1 text-grey-7">
  Since then, Valencia Property has helped thousands of people find their perfect home in and
  around Valencia. What sets us apart is our highly personalized, friendly and informal service
  combined with extreme professionalism. Twenty years of experience means there is hardly anything
  that surprises us.
</p>
```

**Third Paragraph:**
```vue
<p class="text-body1 text-grey-7">
  Now, Graham is introducing a fun way to engage with the Valencia housing market: a house price
  guessing game. Test your real estate instincts on actual Valencia properties and learn about
  the local market. Contact Graham and the team at <strong>+34 ***********</strong> or
  <strong><EMAIL></strong>. Visit <a href="https://www.valencia-property.com" target="_blank"><strong>valencia-property.com</strong></a> for more information.
</p>
```

## Testing Checklist
- [ ] Verify Google Analytics UA-946302-2 is tracking
- [ ] Test email link: <EMAIL>
- [ ] Test phone number link: +34 ***********
- [ ] Test WhatsApp functionality (34657994311)
- [ ] Verify website link opens: valencia-property.com
- [ ] Check all text displays correctly
- [ ] Test on mobile devices
- [ ] Verify no console errors

## Next Steps
1. **CRITICAL**: Manually fix `/src/concerns/valencia/pages/ValenciaHomePage2025.vue` - The file is corrupted and needs to be recreated using the content above
2. Replace placeholder logo with Valencia Property logo
3. Test all tracking codes
4. Deploy to staging environment for review
5. Get Graham's approval before production deployment

## Notes
- The file corruption occurred during automated edits
- Recommend using the Cameron file as a template and manually updating all Cameron references to Graham/Valencia
- Ensure all emojis are properly encoded to avoid parsing errors
- The subdomain config and layout files are already correctly updated
