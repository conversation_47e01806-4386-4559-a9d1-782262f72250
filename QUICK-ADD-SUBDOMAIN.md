# 🚀 Quick Start: Add a New Subdomain in 5 Minutes

## The 5-Step Process

### Step 1: Add to Configuration (2 min)

Edit `src/apps/hpg-main/router/subdomain-config.js`:

```javascript
export const SUBDOMAIN_BRANDS = {
  // ... existing brands ...

  YOURBRAND: {
    name: 'YOURB<PERSON><PERSON>',
    displayName: 'Your Brand Name',
    homeUrl: 'https://YOURBRAND.housepriceguess.com/',
    serviceEmail: '<EMAIL>',
    whitelabelName: 'HousePriceGuess',
    logoUrl: '/icons/favicon-128x128.png', // or your logo URL
    layoutComponent: 'src/concerns/YOURBRAND/layouts/YourBrandLayout.vue',
    homePageComponent: 'src/concerns/YOURBRAND/pages/YourBrandHomePage2025.vue',
    brandedGameSlug: 'YOURBRAND-house-prices-game',
    description: 'Your Brand Description'
  }
}
```

### Step 2: Create Folders (30 sec)

```bash
mkdir -p src/concerns/YOURBRAND/layouts
mkdir -p src/concerns/YOURBRAND/pages
```

### Step 3: Copy Templates (30 sec)

```bash
cp src/concerns/cameron/layouts/CameronLayout.vue \
   src/concerns/YOURBRAND/layouts/YourBrandLayout.vue

cp src/concerns/cameron/pages/CameronHomePage2025.vue \
   src/concerns/YOURBRAND/pages/YourBrandHomePage2025.vue
```

### Step 4: Customize (1 min)

In `YourBrandLayout.vue`:
- Line ~69: Change `name: 'CameronLayout'` → `name: 'YourBrandLayout'`
- Line ~7: Update `href` URL
- Line ~9-12: Update logo and alt text
- Line ~94: Update `whitelabelNameDisplay` default
- Line ~129: Update `brandedGameSlug`

In `YourBrandHomePage2025.vue`:
- Search & replace "Cameron Properties" → "Your Brand Name"
- Update game slug in router link
- Customize description text

### Step 5: Test (30 sec)

```bash
# If not running, start dev server
ROOT_FOLDER_NAME=hpg-main quasar dev

# Open browser
http://YOURBRAND.lvh.me:9000
```

✅ Done! Your subdomain is live!

---

## Checklist

Before marking complete:

- [ ] Added to `subdomain-config.js`
- [ ] Created folders in `src/concerns/`
- [ ] Copied template files
- [ ] Updated component names
- [ ] Updated branding (logo, text)
- [ ] Updated game slug
- [ ] Tested at `YOURBRAND.lvh.me:9000`
- [ ] No console errors
- [ ] Branding looks correct
- [ ] Other subdomains still work

---

## Common Mistakes to Avoid

❌ **Don't** use spaces in subdomain name
✅ **Do** use lowercase, hyphens OK

❌ **Don't** forget to update component name in `export default`
✅ **Do** change from `CameronLayout` to your name

❌ **Don't** test with `localhost`
✅ **Do** test with `lvh.me`

❌ **Don't** leave "Cameron" references
✅ **Do** search & replace with your brand

❌ **Don't** skip testing other subdomains
✅ **Do** verify bvh, costa, star still work

---

## Troubleshooting

### Problem: Subdomain not loading

**Check:**
1. Is subdomain in `subdomain-config.js`?
2. Are file paths correct in config?
3. Do component files exist?
4. Using `lvh.me` not `localhost`?
5. Dev server running?

### Problem: Wrong branding shows

**Check:**
1. Component name updated?
2. Search/replaced "Cameron"?
3. Logo URL correct?
4. Game slug updated?
5. Hard refresh browser (Cmd+Shift+R)?

### Problem: 404 errors

**Check:**
1. File paths in config match actual files
2. Component files in correct location
3. File extensions are `.vue`
4. No typos in paths

---

## Quick Commands

```bash
# Start dev
ROOT_FOLDER_NAME=hpg-main quasar dev

# Test subdomains
http://YOURBRAND.lvh.me:9000   # Your new one
http://bvh.lvh.me:9000          # Test existing
http://cameron.lvh.me:9000      # Test example
http://lvh.me:9000              # Test default

# Build
ROOT_FOLDER_NAME=hpg-main quasar build
```

---

## Files You Created

After following steps above, you'll have:

```
src/concerns/YOURBRAND/
├── layouts/
│   └── YourBrandLayout.vue      ← Your layout
└── pages/
    └── YourBrandHomePage2025.vue ← Your homepage
```

Plus one entry in:
```
src/apps/hpg-main/router/subdomain-config.js
```

That's it! 🎉

---

## Need More Help?

📖 Full Guide: `docs/SUBDOMAIN-CONFIG-GUIDE.md`
📖 Architecture: `docs/SUBDOMAIN-ARCHITECTURE.md`
📖 Example: `CAMERON-IMPLEMENTATION.md`
🧪 Testing: `TEST-CAMERON-SUBDOMAIN.js`

---

**Time to Add New Subdomain**: ~5 minutes
**Difficulty**: Easy ⭐
**Skills Needed**: Basic text editing
