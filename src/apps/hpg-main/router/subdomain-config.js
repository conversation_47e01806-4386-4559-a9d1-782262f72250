/**
 * Centralized Subdomain Configuration
 *
 * This file defines all branded subdomains and their properties.
 * To add a new subdomain, simply add a new entry to the SUBDOMAIN_BRANDS object.
 */

export const SUBDOMAIN_BRANDS = {
  star: {
    name: 'star',
    displayName: 'Star Team',
    homeUrl: 'https://star.housepriceguess.com/',
    serviceEmail: '<EMAIL>',
    whitelabelName: 'HousePriceGuess',
    logoUrl: '/icons/favicon-128x128.png',
    layoutComponent: 'src/concerns/hpg/layouts/StarTeamLayout.vue',
    homePageComponent: 'src/concerns/hpg/pages/StarTeamHomePage2025.vue',
    brandedGameSlug: 'starhometeam-house-prices-game',
    description: 'Team-based property gaming experience'
  },
  bvh: {
    name: 'bvh',
    displayName: 'Buena Vista Homes',
    homeUrl: 'https://bvh.housepriceguess.com/',
    serviceEmail: '<EMAIL>',
    whitelabelName: 'HousePriceGuess',
    logoUrl: 'https://www.buenavistahomes.eu/packs/media/images/buenavista-logo-header-542fb3999ff7b04a4dd1c745a5f4b4cf.svg',
    layoutComponent: 'src/concerns/bvh/layouts/BvhLayout.vue',
    homePageComponent: 'src/concerns/bvh/pages/BvhHomePage2025.vue',
    brandedGameSlug: 'bvh-house-prices-game',
    description: 'Real estate agency branded experience'
  },
  cameron: {
    name: 'cameron',
    displayName: 'Cameron Zatz-Krecker',
    homeUrl: 'https://cameron.housepriceguess.com/',
    serviceEmail: '<EMAIL>',
    whitelabelName: 'Cameron Zatz-Krecker - SPACE',
    logoUrl: '/icons/favicon-128x128.png', // You can replace with Cameron's actual logo URL
    layoutComponent: 'src/concerns/cameron/layouts/CameronLayout.vue',
    homePageComponent: 'src/concerns/cameron/pages/CameronHomePage2025.vue',
    brandedGameSlug: 'arizona-house-price-guess',
    description: 'Cameron Zatz-Krecker, REALTOR® at SPACE - KP Elite | Zaback Group',
    phone: '(*************',
    company: 'SPACE - KP Elite | Zaback Group',
    socialMedia: {
      facebook: 'https://www.facebook.com/spaceteamre',
      instagram: 'https://www.instagram.com/spaceteamre',
      youtube: 'https://www.youtube.com/@spaceteamre',
      linkedin: 'https://www.linkedin.com/company/spaceteamre'
    }
  },
  valencia: {
    name: 'valencia',
    displayName: 'Graham Hunt - Valencia Property',
    homeUrl: 'https://valencia.housepriceguess.com/',
    serviceEmail: '<EMAIL>',
    whitelabelName: 'Valencia Property',
    logoUrl: '/icons/favicon-128x128.png', // You can replace with Valencia Property's actual logo URL
    layoutComponent: 'src/concerns/valencia/layouts/ValenciaLayout.vue',
    homePageComponent: 'src/concerns/valencia/pages/ValenciaHomePage2025.vue',
    brandedGameSlug: 'valencia-house-prices-game',
    description: 'Graham Hunt - Valencia Property - Expert Real Estate Services in Valencia, Spain',
    phone: '+34 ***********',
    whatsapp: '34657994311',
    company: 'Valencia Property',
    website: 'https://www.valencia-property.com',
    googleAnalyticsId: 'UA-946302-2',
    location: 'Valencia, Spain'
  },
  costa: {
    name: 'costa',
    displayName: 'Costa',
    homeUrl: 'https://costa.housepriceguess.com/',
    serviceEmail: '<EMAIL>',
    whitelabelName: 'HousePriceGuess',
    logoUrl: '/icons/favicon-128x128.png',
    layoutComponent: 'src/concerns/costa/layouts/CostaLayout.vue',
    homePageComponent: 'src/concerns/costa/pages/CostaHomePage2025.vue',
    brandedGameSlug: 'costa-house-prices-game',
    description: 'Regional property focus'
  }
}

// Default configuration for non-branded subdomain
export const DEFAULT_BRAND = {
  name: 'default',
  displayName: 'HousePriceGuess',
  homeUrl: 'https://housepriceguess.com/',
  serviceEmail: '<EMAIL>',
  whitelabelName: 'HousePriceGuess',
  logoUrl: '/icons/favicon-128x128.png',
  layoutComponent: 'src/concerns/hpg/layouts/HpgLayout.vue',
  homePageComponent: 'src/concerns/hpg/pages/HpgHomePage2025.vue',
  brandedGameSlug: 'house-prices-game',
  description: 'Default property gaming experience'
}

/**
 * Get brand configuration by subdomain name
 * @param {string} subdomainName - The subdomain name (e.g., 'bvh', 'cameron')
 * @returns {object} Brand configuration object
 */
export function getBrandConfig(subdomainName) {
  return SUBDOMAIN_BRANDS[subdomainName] || DEFAULT_BRAND
}

/**
 * Check if a subdomain is a valid brand
 * @param {string} subdomainName - The subdomain name to check
 * @returns {boolean}
 */
export function isValidBrand(subdomainName) {
  return subdomainName in SUBDOMAIN_BRANDS
}

/**
 * Get all registered brand subdomain names
 * @returns {string[]} Array of subdomain names
 */
export function getAllBrandNames() {
  return Object.keys(SUBDOMAIN_BRANDS)
}
