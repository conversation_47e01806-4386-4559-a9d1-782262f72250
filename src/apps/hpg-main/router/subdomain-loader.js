/**
 * Dynamic Component Loader for Subdomain-based Routing
 *
 * This utility provides functions to dynamically load components based on subdomain
 * using the centralized configuration with explicit imports (Vite-compatible).
 */

import { getCurrentBrandConfig } from './subdomain-utils'

/**
 * Layout component map - explicit imports for Vite compatibility
 */
const LAYOUT_IMPORTS = {
  star: () => import('../../../concerns/hpg/layouts/StarTeamLayout.vue'),
  bvh: () => import('../../../concerns/bvh/layouts/BvhLayout.vue'),
  cameron: () => import('../../../concerns/cameron/layouts/CameronLayout.vue'),
  valencia: () => import('../../../concerns/valencia/layouts/ValenciaLayout.vue'),
  costa: () => import('../../../concerns/costa/layouts/CostaLayout.vue'),
  default: () => import('../../../concerns/hpg/layouts/HpgLayout.vue')
}

/**
 * Homepage component map - explicit imports for Vite compatibility
 */
const HOMEPAGE_IMPORTS = {
  star: () => import('../../../concerns/hpg/pages/StarTeamHomePage2025.vue'),
  bvh: () => import('../../../concerns/bvh/pages/BvhHomePage2025.vue'),
  cameron: () => import('../../../concerns/cameron/pages/CameronHomePage2025.vue'),
  valencia: () => import('../../../concerns/valencia/pages/ValenciaHomePage2025.vue'),
  costa: () => import('../../../concerns/costa/pages/CostaHomePage2025.vue'),
  default: () => import('../../../concerns/hpg/pages/HpgHomePage2025.vue')
}

/**
 * Get the layout component for the current subdomain
 * @returns {Promise} Dynamic import promise
 */
export function getSubdomainLayout() {
  const config = getCurrentBrandConfig()
  const brandName = config?.name || 'default'
  const importFn = LAYOUT_IMPORTS[brandName] || LAYOUT_IMPORTS.default
  return importFn()
}

/**
 * Get the homepage component for the current subdomain
 * @returns {Promise} Dynamic import promise
 */
export function getSubdomainHomePage() {
  const config = getCurrentBrandConfig()
  const brandName = config?.name || 'default'
  const importFn = HOMEPAGE_IMPORTS[brandName] || HOMEPAGE_IMPORTS.default
  return importFn()
}

/**
 * Generic helper to load a component by brand name and type
 * @param {string} brandName - The brand name (star, bvh, cameron, costa)
 * @param {string} componentType - 'layout' or 'homepage'
 * @returns {Promise} Dynamic import promise
 */
export function loadSubdomainComponent(brandName, componentType = 'layout') {
  if (componentType === 'layout') {
    const importFn = LAYOUT_IMPORTS[brandName] || LAYOUT_IMPORTS.default
    return importFn()
  }

  if (componentType === 'homepage') {
    const importFn = HOMEPAGE_IMPORTS[brandName] || HOMEPAGE_IMPORTS.default
    return importFn()
  }

  throw new Error(`Invalid component type: ${componentType}. Must be 'layout' or 'homepage'`)
}
