// Utility functions for subdomain-based routing decisions
// Safe for browser and SSR environments

import { SUBDOMAIN_BRANDS, getBrandConfig, isValidBrand } from './subdomain-config'

function stripPort(host) {
  // Accepts host or hostname; removes :port if present
  if (!host) return ''
  return host.split(':')[0]
}

function parseSubdomainFromHost(hostname) {
  const host = stripPort(hostname || '')
  if (!host) return ''
  const isIpHost = /^\d+(?:\.\d+){3}$/.test(host)
  const parts = host.split('.')
  if (!isIpHost && parts.length > 2) return parts[0]
  if (!isIpHost && parts.length === 2 && host !== 'localhost') return parts[0]
  return ''
}

function resolveSubdomain(explicitHostname) {
  // Priority:
  // 1) Explicit hostname passed in
  // 2) Global provided by boot (server or client)
  // 3) window.location.hostname (client only)
  // 4) Fallback to empty string
  const subFromArg = parseSubdomainFromHost(explicitHostname)
  if (subFromArg) return subFromArg

  // Globals set by boot/pwb-flex-conf.js
  const g = typeof globalThis !== 'undefined' ? globalThis : {}
  const fromGlobal =
    (g.__SUBDOMAIN_NAME__ && String(g.__SUBDOMAIN_NAME__)) ||
    (g.__PWB_FLEX_CONFIG__ && g.__PWB_FLEX_CONFIG__.subdomainName)
  if (fromGlobal) return String(fromGlobal)

  if (typeof window !== 'undefined' && window.location) {
    return parseSubdomainFromHost(window.location.hostname)
  }
  return ''
}

export function getSubdomainFromHostname(hostname) {
  try {
    return resolveSubdomain(hostname)
  } catch (e) {
    return ''
  }
}

// Legacy individual check functions (kept for backward compatibility)
export function isStarSubdomain(hostname) {
  return getSubdomainFromHostname(hostname) === 'star'
}

export function isBvhSubdomain(hostname) {
  return getSubdomainFromHostname(hostname) === 'bvh'
}

export function isCameronSubdomain(hostname) {
  return getSubdomainFromHostname(hostname) === 'cameron'
}

export function isValenciaSubdomain(hostname) {
  return getSubdomainFromHostname(hostname) === 'valencia'
}

export function isCostaSubdomain(hostname) {
  return getSubdomainFromHostname(hostname) === 'costa'
}

// Generic check function using configuration
export function isBrandSubdomain(brandName, hostname) {
  return getSubdomainFromHostname(hostname) === brandName
}

export function whichBrandSubdomain(hostname) {
  const sub = getSubdomainFromHostname(hostname)
  return isValidBrand(sub) ? sub : ''
}

// New: Get the full brand configuration for current subdomain
export function getCurrentBrandConfig(hostname) {
  const subdomain = getSubdomainFromHostname(hostname)
  return getBrandConfig(subdomain)
}
