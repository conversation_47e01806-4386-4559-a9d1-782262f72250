import { computed } from 'vue'

/**
 * Generic session resolver that centralises the logic for deriving the
 * "effective" session id for price-game/realty-game flows. It works with any
 * storage composable that exposes the minimal contract implemented by
 * useRealtyGameStorage and useKlavoGameStorage.
 */
export function useGameSessionResolver({
  storage,
  route,
  propSessionId = '',
  queryKey = 'session',
  requireSingleton = true,
} = {}) {
  if (!storage) {
    throw new Error('useGameSessionResolver requires a storage instance')
  }

  const {
    currentSessionId,
    getCurrentSessionId,
    isValidSessionId,
    hasSessionDataFor,
    getStoredSessionIds,
  } = storage

  const urlSessionId = computed(() => {
    const fromProp = (propSessionId || '') + ''
    const fromQuery = route?.query?.[queryKey]
    return (fromProp || fromQuery || '') + ''
  })

  const storedCurrentId = computed(() => {
    if (currentSessionId && 'value' in currentSessionId && currentSessionId.value) {
      return currentSessionId.value
    }
    if (typeof getCurrentSessionId === 'function') {
      return getCurrentSessionId() || ''
    }
    return ''
  })

  const effectiveSessionId = computed(() => {
    const sid = urlSessionId.value
    if (sid && isValidSessionId?.(sid) && hasSessionDataFor?.(sid)) {
      return sid
    }
    const stored = storedCurrentId.value
    if (stored && isValidSessionId?.(stored) && hasSessionDataFor?.(stored)) {
      return stored
    }
    return ''
  })

  const sessionIsValid = computed(() => {
    const sid = effectiveSessionId.value
    if (!sid) return false
    if (!isValidSessionId?.(sid) || !hasSessionDataFor?.(sid)) return false
    if (!requireSingleton) return true
    const ids = typeof getStoredSessionIds === 'function' ? getStoredSessionIds() : []
    return Array.isArray(ids) && ids.length === 1 && ids[0] === sid
  })

  const isCurrentUsersSession = computed(() => {
    const active =
      (currentSessionId && 'value' in currentSessionId && currentSessionId.value) ||
      (typeof getCurrentSessionId === 'function' && getCurrentSessionId())
    return !!active && active === effectiveSessionId.value
  })

  return {
    urlSessionId,
    effectiveSessionId,
    sessionIsValid,
    isCurrentUsersSession,
  }
}
