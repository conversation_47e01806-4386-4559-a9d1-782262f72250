import { useKlavoGameStorage } from 'src/concerns/realty-game/composables/useKlavoGameStorage'
import { useGameSessionResolver } from 'src/concerns/realty-game/composables/useGameSessionResolver'

// Resolve the effective session id specifically for Klavo game pages
export function useKlavoGameSessionResolver({ route, propSessionId = '' } = {}) {
  const storage = useKlavoGameStorage()
  return useGameSessionResolver({
    storage,
    route,
    propSessionId,
    queryKey: 'session',
    requireSingleton: true,
  })
}

