<template>
  <div class="rug-game-start-page">
    <!-- The <q-no-ssr> wrapper has been removed to enable Server-Side Rendering -->
    <div class="rug-sp-mx-ctr q-pa-sm rgsp q-mb-xl q-pb-xl">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-container text-center q-pa-xl">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-md text-h6">Loading Properties...</div>
        <div class="text-body2 text-grey-7">
          Preparing your price guessing challenge
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container text-center q-pa-xl">
        <q-icon name="error" color="negative" size="3em" />
        <div class="q-mt-md text-h6 text-negative">
          Failed to Load Properties
        </div>
        <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
        <q-btn color="primary" label="Try Again" @click="initializeGame" />
      </div>

      <!-- No Properties State -->
      <div
        v-else-if="storeTotalProperties < 1"
        class="loading-container text-center q-pa-xl"
      >
        <div class="q-mt-md text-h6">
          Sorry, this price guessing challenge is not available yet
        </div>
        <div class="text-body2 text-grey-7">Please check again later</div>
      </div>

      <!-- Game Start -->
      <div v-else class="ru-game-start-container">
        <!-- How It Works -->
        <!-- <q-card class="how-it-works-card q-mb-xl" flat bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h5 text-weight-medium text-center q-mb-lg">
              <q-icon name="help_outline" color="primary" class="q-mr-sm" />
              How It Works
            </div>
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-4">
                <div class="step-card text-center">
                  <div class="step-number">1</div>
                  <div class="step-title">View Property</div>
                  <div class="step-description">
                    See photos and details of real properties currently for sale
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-4">
                <div class="step-card text-center">
                  <div class="step-number">2</div>
                  <div class="step-title">Make Your Guess</div>
                  <div class="step-description">
                    Estimate the asking price based on what you see
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-4">
                <div class="step-card text-center">
                  <div class="step-number">3</div>
                  <div class="step-title">Get Your Score</div>
                  <div class="step-description">
                    See how close you were and compare with other players
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card> -->

        <!-- Property Preview Cards -->
        <div class="rgsp-ctr q-mb-xl" v-if="storeGameListings.length > 0">
          <q-card-section class="q-py-lg q-px-sm">
            <div
              class="text-h6 text-center text-weight-medium q-mb-md"
              style="color: rgb(156, 39, 176)"
            >
              <!-- <q-icon name="home" color="primary" class="q-mr-sm" /> -->
              Properties in This Challenge
            </div>
            <!-- <div class="text-body2 text-grey-7 q-mb-lg">
              Guess the price of whichever one of these catches your fancy
            </div> -->

            <div>
              <!-- Spotted-style listing cards -->
              <div class="branded-game-content q-pt-none q-pb-none q-px-none">
                <div v-if="displayListings.length" class="rug-branded-game-grd">
                  <q-card
                    v-for="listing in displayListings"
                    :key="listing.uuid"
                    class="game-listing-card hoverable hpg-card-star"
                    flat
                    bordered
                    @click="startNavigateToProperty(listing)"
                  >
                    <q-img
                      :src="listing.imageUrl"
                      :ratio="4 / 3"
                      :alt="listing.title"
                      loading="lazy"
                      spinner-color="primary"
                      style="margin-top: -2px"
                    >
                      <div
                        v-if="listing.badges.length"
                        class="absolute-top badges-row row no-wrap q-pa-sm q-gutter-xs"
                      >
                        <q-chip
                          v-for="b in listing.badges"
                          :key="b.label"
                          dense
                          square
                          color="primary"
                          text-color="white"
                          class="badge-chip"
                          >{{ b.label }}</q-chip
                        >
                      </div>
                    </q-img>
                    <q-card-section class="q-pb-none">
                      <div
                        class="rmv-lst-title text-subtitle1 text-weight-medium"
                        :class="{
                          'ellipsis-2-lines': !isTitleExpanded(listing),
                        }"
                        @click.stop="onTitleClick(listing, $event)"
                      >
                        {{ listing.title }}
                      </div>
                    </q-card-section>
                    <q-card-section class="q-pt-sm q-pb-sm">
                      <div
                        class="row items-center text-caption text-grey-8 q-gutter-sm"
                      >
                        <div
                          v-if="listing.listingCity"
                          class="row items-center listing-city"
                        >
                          <q-icon name="place" size="16px" class="q-mr-xs" />
                          <span class="listing-city-text">
                            {{ listing.listingCity }}
                          </span>
                        </div>
                        <div
                          v-if="listing.bedrooms !== null"
                          class="row items-center"
                        >
                          <q-icon name="king_bed" size="16px" class="q-mr-xs" />
                          {{ listing.bedrooms }}
                        </div>
                        <div
                          v-if="listing.bathrooms !== null"
                          class="row items-center"
                        >
                          <q-icon name="bathtub" size="16px" class="q-mr-xs" />
                          {{ listing.bathrooms }}
                        </div>
                        <div
                          v-if="listing.garages !== null"
                          class="row items-center"
                        >
                          <q-icon name="garage" size="16px" class="q-mr-xs" />
                          {{ listing.garages }}
                        </div>
                      </div>
                    </q-card-section>
                    <q-separator inset />
                    <q-card-actions
                      align="between"
                      class="q-px-sm q-pb-sm q-pt-xs"
                    >
                      <div
                        v-if="listing.guessData"
                        class="column q-gutter-xs full-width q-px-sm q-py-xs rounded-borders guess-summary"
                      >
                        <!-- <div
                          class="row items-center no-wrap q-gutter-xs text-caption text-primary text-weight-medium"
                        >
                          <q-icon name="check_circle" size="16px" color="positive" />
                          <span>Guess saved</span>
                        </div> -->
                        <div
                          v-if="hasActualPrice(listing.guessData)"
                          class="row items-center q-col-gutter-sm text-caption text-grey-7 guess-meta"
                        >
                          <span>Listed price: </span>
                          <span class="text-weight-medium text-grey-8">
                            {{ formatActualPrice(listing.guessData) }}
                          </span>
                        </div>
                        <div
                          class="row items-center q-col-gutter-sm text-caption text-grey-7 guess-meta"
                        >
                          <span
                            >You guessed:
                            {{ formatGuessAmount(listing.guessData) }}
                          </span>
                        </div>
                        <!-- <div class="text-body2 text-weight-medium">
                          {{ formatGuessAmount(listing.guessData) }}
                        </div> -->
                        <!-- <div
                          class="row items-center q-col-gutter-sm text-caption text-grey-7 guess-meta"
                        >
                          <span>Score: {{ listing.guessData.score }}/100</span>
                          <span
                            v-if="formatGuessDifference(listing.guessData)"
                            :class="guessDifferenceClass(listing.guessData)"
                          >
                            Δ {{ formatGuessDifference(listing.guessData) }}
                          </span>
                          <span v-if="listing.guessData.submittedAt">
                            {{ formatGuessDate(listing.guessData.submittedAt) }}
                          </span>
                        </div> -->
                      </div>
                      <q-btn
                        v-else
                        color="primary"
                        size="sm"
                        flat
                        label="Guess the price"
                        @click.stop="startNavigateToProperty(listing)"
                      />
                    </q-card-actions>
                  </q-card>
                </div>
                <div v-else class="text-grey-7 q-pa-lg text-center">
                  No listings available for this game yet.
                </div>
              </div>
            </div>
            <!-- <div class="properties-grid">
              <div
                v-for="(gameListing, index) in storeGameListings"
                :key="gameListing.listing_details?.uuid || index"
                class="property-card-item"
                @click="startNavigateToProperty(gameListing)"
              >
                <PropertyPreviewCardItem
                  :listing="gameListing"
                  :getPropertyMainImage="getPropertyMainImage"
                  :getPropertyAddress="getPropertyAddress"
                  @start="startNavigateToProperty(gameListing)"
                />
              </div>
            </div> -->
          </q-card-section>
        </div>

        <!-- User Info Collection -->
        <q-card class="user-info-card q-mb-xl" flat bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 text-weight-medium q-mb-md">
              <q-icon name="person" color="primary" class="q-mr-sm" />
              Player Information
            </div>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="playerName"
                  label="Your name"
                  outlined
                  dense
                  placeholder="Enter your name or leave blank"
                  hint="This helps us show you how you compare to other players"
                >
                  <template v-slot:prepend>
                    <q-icon name="person" />
                  </template>
                </q-input>
              </div>
              <div class="col-12 col-md-6 currency-selector">
                <q-select
                  v-model="selectedCurrency"
                  :options="currencyOptions"
                  option-label="label"
                  option-value="code"
                  emit-value
                  map-options
                  outlined
                  dense
                  label="Currency"
                  hint="Choose your preferred currency for prices"
                >
                  <template v-slot:prepend>
                    <q-icon name="currency_exchange" />
                  </template>
                  <template v-slot:option="scope">
                    <q-item v-bind="scope.itemProps">
                      <q-item-section avatar>
                        <span class="currency-symbol">{{
                          scope.opt.symbol
                        }}</span>
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ scope.opt.name }}</q-item-label>
                        <q-item-label caption>{{
                          scope.opt.code
                        }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                  <template v-slot:selected>
                    <span class="selected-currency" v-if="selectedCurrencyData">
                      {{ selectedCurrencyData.symbol }}
                      {{ selectedCurrencyData.name }}
                    </span>
                  </template>
                </q-select>
              </div>
            </div>
            <div class="text-caption text-grey-6 q-mt-md">
              Your guesses will be saved and compared with other players. No
              personal information is required.
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { useRealtyGameStorage } from '../../composables/useRealtyGameStorage'
import { useCurrencyConverter } from '../../composables/useCurrencyConverter'
import { usePlayerName } from '../../composables/usePlayerName'
import { useRealtyGame } from '../../composables/useRealtyGame'
import { useRealtyGameStore } from 'src/stores/realtyGame'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
// import PropertyPreviewCardItem from 'src/concerns/rug-game/components/roundup/PropertyPreviewCardItem.vue'
import logoUrl from '/icons/favicon-128x128.png'

export default {
  name: 'RoundupGameStartPage',
  components: {
    // PropertyPreviewCardItem,
  },
  props: {
    // shareableResultsUrl: {
    //   type: String,
    //   required: true,
    // },
  },
  // preFetch hook for SSR and data prefetching
  async preFetch({ currentRoute, ssrContext }) {
    console.log('preFetch running on server for RoundupGameStartPage...')
    const gameSlug = currentRoute.params.gameSlug
    if (!gameSlug) return

    try {
      const store = useRealtyGameStore()
      const metaStore = useRealtyGameMetaStore()
      const { fetchAndSetGameData } = useRealtyGame()
      const gameData = await fetchAndSetGameData(gameSlug, store)
      // Set meta tags in the meta store
      console.log(`setting metastore with ${gameData.gameTitle}`)

      if (gameData) {
        metaStore.setMeta({
          title: `Start ${gameData.gameTitle} - Test Your Property Knowledge`,
          description:
            gameData.gameDesc ||
            'Test your property market knowledge with our interactive price guessing game.',
          image:
            gameData.gameBgImageUrl ||
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          url: `${pwbFlexConfig.siteBaseUrl}${currentRoute.fullPath}`,
          keywords: `property price game, real estate challenge, property valuation, house price quiz, market knowledge test, ${
            gameData.gameTitle || ''
          }`,
        })
      }
      console.log(`MetaStore title now: ${metaStore.title}`)

      if (ssrContext) {
        ssrContext.realtyGameData = gameData
      }
    } catch (error) {
      console.error('preFetch error for start page:', error)
      // You could set an error state in the store here
    }
  },

  setup() {
    const $router = useRouter()
    const $route = useRoute()
    const $q = useQuasar()
    const realtyGameStore = useRealtyGameStore()
    // const metaStore = useRealtyGameMetaStore()

    // Local state for UI (loading, errors)
    const isLoading = ref(false)
    const error = ref(null)

    // Computed properties to read from the Pinia store
    const storeGameTitle = computed(() => realtyGameStore.gameTitle)
    const storeTotalProperties = computed(
      () => realtyGameStore.getTotalProperties
    )
    const storeBgImageUrl = computed(() => realtyGameStore.gameBgImageUrl)
    // const firstPropListing = computed(() => realtyGameStore.firstVisibleListing)
    const storeDefaultCurrency = computed(
      () => realtyGameStore.gameDefaultCurrency
    )
    const storeGameListings = computed(() => realtyGameStore.gameListings)

    // --- Composables ---
    const {
      getOrCreateSessionId,
      saveSessionData,
      getSessionData,
      saveCurrencySelection,
      getCurrencySelection,
      getCurrentSessionGuesses,
    } = useRealtyGameStorage()
    const {
      selectedCurrency,
      availableCurrencies,
      selectedCurrencyData,
      setCurrency,
      formatPriceWithBothCurrencies,
    } = useCurrencyConverter()
    const { playerName, updatePlayerName, initializePlayerName } =
      usePlayerName()
    const { fetchAndSetGameData } = useRealtyGame()

    // --- Game Initialization ---
    const gameSlug = computed(() => $route.params.gameSlug)

    const initializeGame = async () => {
      if (!gameSlug.value) return
      isLoading.value = true
      error.value = null
      try {
        await fetchAndSetGameData(gameSlug.value, realtyGameStore)
      } catch (err) {
        error.value = err.message || 'An unknown error occurred.'
        $q.notify({
          color: 'negative',
          message: 'Failed to load property data',
          icon: 'error',
        })
      } finally {
        isLoading.value = false
      }
    }

    onMounted(() => {
      // If data wasn't loaded by preFetch (e.g., client-side navigation), load it now.
      if (!realtyGameStore.isDataLoaded) {
        initializeGame()
      }

      // Initialize player name from storage
      initializePlayerName()

      // Initialize currency
      const sessionData = getSessionData()
      const storedCurrency = getCurrencySelection()
      setCurrency(
        sessionData.selectedCurrency ||
          storedCurrency ||
          storeDefaultCurrency.value
      )
    })

    // --- Watchers ---
    watch(playerName, (newValue) => {
      updatePlayerName(newValue)
    })

    watch(selectedCurrency, (newValue) => {
      if (newValue) {
        saveCurrencySelection(newValue)
        saveSessionData({ selectedCurrency: newValue })
      }
    })

    // --- Computed Properties for Template ---
    const currencyOptions = computed(() =>
      availableCurrencies.value.map((currency) => ({
        code: currency.code,
        name: currency.name,
        symbol: currency.symbol,
        label: `${currency.symbol} ${currency.name} (${currency.code})`,
      }))
    )

    const currentSessionGuesses = computed(() => {
      const guesses = getCurrentSessionGuesses()
      return guesses || {}
    })

    // // Game completion logic
    // const currentSessionGuesses = computed(() => getCurrentSessionGuesses())
    // // Get the UUIDs of properties in the current game
    // const gamePropertyUuids = computed(() => {
    //   // 11 july 2025: sometimes listing.listing_details is
    //   // missing hence listing.listing_details? below
    //   return realtyGameStore.gameListings.map(
    //     (listing) => listing.listing_details?.uuid
    //   )
    // })
    // const filteredGuesses = computed(() => {
    //   const guesses = currentSessionGuesses.value
    //   const uuids = gamePropertyUuids.value
    //   return Object.keys(guesses).filter((uuid) => uuids.includes(uuid))
    // })
    // const completedGuessCount = computed(() => filteredGuesses.value.length)
    // const isGameCompleted = computed(() => {
    //   const totalProps = storeTotalProperties.value
    //   const guessCount = completedGuessCount.value
    //   return totalProps > 0 && guessCount >= totalProps
    // })

    const backgroundStyle = computed(() => {
      const baseStyle = {
        padding: '0px',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }
      if (storeBgImageUrl.value) {
        return {
          ...baseStyle,
          backgroundImage: `url(${storeBgImageUrl.value})`,
        }
      }
      // Fallback gradient
      const gradients = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      ]
      const index = gameSlug.value
        ? gameSlug.value.charCodeAt(0) % gradients.length
        : 0
      return { ...baseStyle, background: gradients[index] }
    })

    // --- Methods ---
    const startNavigateToProperty = (listingInGame) => {
      // Simply navigate to the property page - player name validation will happen there
      navigateToProperty(listingInGame)
    }

    // Property card helper methods
    const getPropertyMainImage = (listing) => {
      return listing.gl_image_url || listing.gl_image_url_atr
      // const images = listing.listing_details?.sale_listing_pics || []
      // if (listing.gl_image_url_atr) {
      //   return listing.gl_image_url_atr
      // }
      // if (images.length > 0 && !images[0].flag_is_hidden) {
      //   return (
      //     images[0].image_details?.small_fit?.url ||
      //     images[0].image_details?.url ||
      //     images[0].photo_slug ||
      //     'https://dummyimage.com/300x200/f5f5f5/999999?text=No+Image'
      //   )
      // }
      // return 'https://dummyimage.com/300x200/f5f5f5/999999?text=No+Image'
    }

    const getPropertyAddress = (listing) => {
      const details = listing.listing_details
      if (!details) return 'Address not available'

      const parts = []
      if (details.street_address) parts.push(details.street_address)
      if (details.city) parts.push(details.city)
      if (details.postal_code) parts.push(details.postal_code)

      return parts.length > 0 ? parts.join(', ') : 'Address not available'
    }

    const navigateToProperty = (listingInGame) => {
      // Check if we have a valid UUID (either parent UUID or listing_details UUID)
      if (!listingInGame.uuid && !listingInGame.listing_details?.uuid) return

      const routeSssnId = getOrCreateSessionId()
      saveSessionData({
        playerName: playerName.value || 'Anonymous Player',
        totalProperties: storeTotalProperties.value,
        gameTitle: storeGameTitle.value,
        selectedCurrency: selectedCurrency.value,
        startedAt: new Date().toISOString(),
      })
      saveCurrencySelection(selectedCurrency.value)

      $router.push({
        name: 'rRoundupGameProperty',
        params: {
          gameSlug: gameSlug.value,
          listingInGameUuid: listingInGame.uuid,
        },
        query: { session: routeSssnId },
      })
    }

    // --- Spotted-style listing mapping and title toggle ---
    const displayListings = computed(() => {
      const guesses = currentSessionGuesses.value
      return storeGameListings.value.map((g, index) => {
        const ld = g.listing_details || {}
        const uuid = g.uuid || ld.uuid
        const title =
          g.gl_title_atr || ld.listing_title || ld.title || 'Untitled Property'
        const listingCity = ld.listing_city || ld.city || ''
        const bedrooms = ld.listing_count_bedrooms ?? ld.count_bedrooms ?? null
        const bathrooms =
          ld.listing_count_bathrooms ?? ld.count_bathrooms ?? null
        const garages = ld.listing_count_garages ?? ld.count_garages ?? null
        const imageUrl = getPropertyMainImage(g)
        const guessData = uuid ? guesses[uuid] || null : null
        return {
          uuid,
          index,
          title,
          listingCity,
          bedrooms,
          bathrooms,
          garages,
          imageUrl,
          badges: [],
          guessData,
        }
      })
    })

    const formatGuessAmount = (guessData) => {
      if (!guessData || guessData.guess == null) {
        return 'Guess saved'
      }

      const guessNumber = Number(guessData.guess)
      if (!Number.isFinite(guessNumber) || guessNumber <= 0) {
        return 'Guess saved'
      }

      const guessInCents = Math.round(guessNumber * 100)

      try {
        return formatPriceWithBothCurrencies(
          guessInCents,
          selectedCurrency.value,
          true,
          guessData.currency
        )
      } catch (error) {
        const symbol = selectedCurrencyData.value?.symbol || ''
        return `${symbol}${guessNumber.toLocaleString()}`
      }
    }

    const hasActualPrice = (guessData) => {
      const actualPrice = Number(guessData?.actualPrice)
      return Number.isFinite(actualPrice) && actualPrice > 0
    }

    const formatActualPrice = (guessData) => {
      if (!hasActualPrice(guessData)) {
        return null
      }

      const actualPriceNumber = Number(guessData.actualPrice)
      const actualCurrency = guessData.currency || selectedCurrency.value
      const priceInCents = Math.round(actualPriceNumber * 100)

      try {
        return formatPriceWithBothCurrencies(
          priceInCents,
          actualCurrency,
          true,
          selectedCurrency.value
        )
      } catch (error) {
        if (actualCurrency === selectedCurrency.value) {
          const symbol = selectedCurrencyData.value?.symbol || ''
          return symbol
            ? `${symbol}${actualPriceNumber.toLocaleString()}`
            : actualPriceNumber.toLocaleString()
        }
        return `${actualCurrency} ${actualPriceNumber.toLocaleString()}`
      }
    }

    const formatGuessDifference = (guessData) => {
      const diff = Number(guessData?.difference)
      if (!Number.isFinite(diff)) return null

      const sign = diff > 0 ? '+' : ''
      return `${sign}${diff.toFixed(1)}%`
    }

    const guessDifferenceClass = (guessData) => {
      const diff = Number(guessData?.difference)
      if (!Number.isFinite(diff) || diff === 0) {
        return 'text-grey-7'
      }

      return diff > 0 ? 'text-negative' : 'text-positive'
    }

    const formatGuessDate = (isoString) => {
      if (!isoString) return ''

      try {
        return new Date(isoString).toLocaleDateString(undefined, {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })
      } catch (error) {
        return ''
      }
    }

    const expandedTitleIds = ref(new Set())
    const isTitleExpanded = (listing) =>
      expandedTitleIds.value.has(listing.uuid)
    const toggleTitle = (listing) => {
      const id = listing.uuid
      if (!id) return
      const set = expandedTitleIds.value
      if (set.has(id)) set.delete(id)
      else set.add(id)
      expandedTitleIds.value = new Set(set)
    }
    const isDesktopPointer = () =>
      typeof window !== 'undefined' &&
      window.matchMedia &&
      window.matchMedia('(hover: hover) and (pointer: fine)').matches
    const onTitleClick = (listing, evt) => {
      if (isDesktopPointer()) {
        evt.stopPropagation()
        toggleTitle(listing)
      }
    }

    return {
      isLoading,
      error,
      playerName,
      selectedCurrency,
      selectedCurrencyData,
      currencyOptions,
      storeGameTitle,
      storeTotalProperties,
      storeGameListings,
      backgroundStyle,
      initializeGame,
      // startGame,
      logoUrl, // Expose logoUrl to the template
      // Game completion properties and methods
      // isGameCompleted,
      // completedGuessCount,
      // viewResults,
      // shareResults,
      // inviteOthers,
      // Property card methods
      getPropertyMainImage,
      getPropertyAddress,
      navigateToProperty,
      startNavigateToProperty,
      // Spotted-style list
      displayListings,
      formatGuessAmount,
      formatGuessDifference,
      guessDifferenceClass,
      formatGuessDate,
      hasActualPrice,
      formatActualPrice,
      isTitleExpanded,
      onTitleClick,
    }
  },
}
</script>
<style scoped>
/* Your existing styles are fine and have been kept */
.game-overlay {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  padding: 32px;
  height: 100%;
  border-radius: 12px;
}

@media (max-width: 768px) {
  .game-overlay {
    padding: 24px;
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .game-overlay {
    padding: 20px;
    min-height: 450px;
  }
}

.rug-game-start-page {
  min-height: 100vh;
}

.rug-sp-mx-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.ru-game-start-container {
  padding-top: 2rem;
}

.welcome-section {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-icon {
  border-radius: 50%;
  width: 110px;
  height: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  /* Subtle background for logo */
}

.hpg-logo-img {
  height: 70px;
  /* Slightly larger than the original 60px from PriceGuessGameLayout.vue */
  width: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  /* Slightly stronger shadow for depth */
  background: #fff;
  padding: 8px;
  /* Padding to match the white background effect */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hpg-logo-img:hover {
  transform: scale(1.1) rotate(-3deg);
  /* Slightly more pronounced hover effect */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  /* Enhanced shadow on hover */
}

.challenge-stats {
  margin-top: 2rem;
}

.how-it-works-card,
.user-info-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.step-card {
  padding: 1.5rem;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.step-description {
  color: #666;
  line-height: 1.5;
}

.start-section {
  background: white;
  border-radius: 12px;
  padding: 3rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.start-button {
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: none;
}

.completed-game-section {
  padding: 1rem 0;
}

.completion-message {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  padding: 2rem;
  border: 2px solid #e8f5e8;
}

.completed-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

.invite-others {
  background: #fafafa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
}

.invite-button {
  font-weight: 500;
}

.currency-symbol {
  font-weight: bold;
  font-size: 1.1rem;
  min-width: 30px;
  text-align: center;
}

.selected-currency {
  font-weight: 500;
}

.guess-summary {
  min-width: 0;
}

.guess-meta span {
  display: inline-flex;
  align-items: center;
}

/* Property Preview Cards */
/* .rgsp-ctr {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
} */

.properties-grid {
  display: grid;
  grid-template-columns: 1fr; /* Updated to make each item full width from below */
  /* grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); */
  gap: 28px; /* Increased from 20px to 28px for more space between cards */
  margin-top: 16px;
}

.property-card-item {
  transition: transform 0.2s ease;
}

.property-card-item:hover {
  transform: translateY(-2px);
}

.property-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.property-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-color: #1976d2;
}

.property-image-container {
  position: relative;
  height: 220px; /* Increased from 200px to make cards taller */
  overflow: hidden;
}

.property-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.property-overlay {
  position: absolute;
  top: 12px;
  right: 12px;
}

.preview-chip {
  backdrop-filter: blur(4px);
  background: rgba(25, 118, 210, 0.9);
}

.property-title {
  font-size: 0.95rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.property-address {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.property-stats {
  margin-top: 8px;
}

.stats-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #666;
}

@media (max-width: 768px) {
  .properties-grid {
    grid-template-columns: 1fr; /* Ensure single column on smaller screens */
    gap: 24px; /* Increased from 16px to 24px for consistency */
  }

  .property-image-container {
    height: 200px; /* Increased from 180px to maintain taller cards */
  }

  .welcome-section {
    padding: 0;
  }

  .start-section {
    padding: 2rem 1rem;
  }

  .start-button {
    width: 100%;
    max-width: 300px;
  }

  .hpg-logo-img {
    height: 60px;
    /* Smaller size for mobile */
  }

  .welcome-icon {
    width: 100px;
    height: 100px;
  }

  .completed-actions {
    flex-direction: column;
    align-items: center;
  }

  .completed-actions .q-btn {
    width: 100%;
    max-width: 280px;
  }

  .completion-message {
    padding: 1.5rem;
  }

  .invite-others {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .properties-grid {
    grid-template-columns: 1fr; /* Ensure single column on very small screens */
    gap: 20px; /* Increased from 12px to 20px for consistency */
  }

  .property-image-container {
    height: 180px; /* Increased from 160px to maintain taller cards */
  }

  .property-title {
    font-size: 0.9rem;
  }
}

/* Spotted-style grid and cards (adapted) */
.branded-game-content {
  padding: 1rem 0 0.5rem;
  margin: 0 auto;
}
.rug-branded-game-grd {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.25rem;
}
.game-listing-card {
  cursor: pointer;
  transition: box-shadow 0.18s ease, transform 0.18s ease;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}
.game-listing-card.hoverable:hover {
  box-shadow: 0 6px 18px -4px rgba(0, 0, 0, 0.18);
  transform: translateY(-4px);
}
.badges-row {
  background: linear-gradient(90deg, rgba(0, 0, 0, 0.55), rgba(0, 0, 0, 0.05));
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.badge-chip {
  font-size: 10px;
  line-height: 1;
  padding: 2px 4px;
}
.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.rmv-lst-title {
  line-height: 1.35em;
  min-height: calc(2 * 1.35em);
  position: relative;
  padding-right: 18px;
}
.rmv-lst-title__toggle {
  position: absolute;
  right: 0;
  bottom: 2px;
  color: #3a933a;
  cursor: pointer;
}
@media (hover: hover) and (pointer: fine) {
  .rmv-lst-title.ellipsis-2-lines {
    cursor: pointer;
  }
}
.rmv-lst-title__toggle:hover {
  opacity: 0.85;
}
.listing-city {
  max-width: 350px;
  overflow: hidden;
  flex-wrap: nowrap;
  align-items: center;
  display: flex;
}
.listing-city-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  flex: 1 1 auto;
}
.listing-city .q-icon {
  flex-shrink: 0;
}
@media (max-width: 768px) {
  .rmv-lst-title__toggle {
    display: none;
  }
  .rmv-lst-title.ellipsis-2-lines {
    display: block;
    -webkit-line-clamp: initial;
    line-clamp: initial;
    -webkit-box-orient: initial;
    overflow: visible;
  }
}
@media (hover: hover) and (pointer: fine) {
  .rmv-lst-title.ellipsis-2-lines:hover {
    display: block;
    -webkit-line-clamp: initial;
    line-clamp: initial;
    -webkit-box-orient: initial;
    overflow: visible;
  }
}
@media (min-width: 640px) {
  .rug-branded-game-grd {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (min-width: 960px) {
  .rug-branded-game-grd {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1280px) {
  .rug-branded-game-grd {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
