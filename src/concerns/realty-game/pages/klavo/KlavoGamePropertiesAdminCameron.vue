<template>
  <div class="price-guess-properties-admin">
    <div class="q-pa-md">
      <!-- <div class="row justify-between items-center q-mb-lg">
        <div>
          <h4 class="q-my-none">Price Guess Game - Property Management...</h4>
          <p class="text-grey-6 q-mb-none">
            Manage property visibility and titles for the price guessing game
          </p>
        </div>
        <div class="row q-gutter-sm">
          <q-btn
            color="positive"
            icon="add"
            label="Create New Game - coming soon"
            @click="showCreateComingSoon"
          />
          <q-btn
            color="primary"
            icon="refresh"
            label="Refresh Data"
            @click="refreshData"
            :loading="isLoading"
          />
        </div>
      </div> -->

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center q-py-xl">
        <q-spinner-dots size="50px" color="primary" />
        <p class="q-mt-md text-grey-6">Loading properties...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center q-py-xl">
        <q-icon name="error" size="50px" color="negative" />
        <p class="q-mt-md text-negative">{{ error }}</p>
        <q-btn color="primary" @click="refreshData" label="Try Again" />
      </div>

      <!-- Properties List -->
      <div v-else-if="adminProperties.length > 0">
        <!-- Game Details Admin Section -->
        <RealtyGameDetailsAdminCard
          :realtyGameSummary="realtyGameSummary"
          :showLimitedView="true"
          @saved="onGameDetailsSaved"
        />
        <!-- Quick navigation list to Cameron property edit pages -->
        <div class="q-mb-lg">
          <h6 class="text-subtitle2 q-mt-none q-mb-sm">
            Cameron Property Edit Links
            <q-badge class="q-ml-sm" color="primary" :label="adminProperties.length" />
          </h6>
          <q-list dense bordered class="rounded-borders">
            <q-item
              v-for="realtyGameListing in adminProperties"
              :key="realtyGameListing.uuid"
              clickable
              :to="{
                name: 'rKlavoGamePropertyCameron',
                params: {
                  gameSlug: $route.params.gameSlug,
                  listingInGameUuid: realtyGameListing.uuid,
                },
              }"
            >
              <q-item-section>
                <q-item-label>
                  {{
                    realtyGameListing.listing_details?.title ||
                      realtyGameListing.listing_details?.display_address ||
                      'Property'
                  }}
                </q-item-label>
                <q-item-label
                  caption
                  v-if="realtyGameListing.listing_details?.display_address"
                >
                  {{ realtyGameListing.listing_details.display_address }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon name="open_in_new" size="16px" />
              </q-item-section>
            </q-item>
          </q-list>
        </div>
<!--
        <div class="q-mb-md">
          <q-badge
            color="green"
            :label="`${gameListings.length} visible Properties`"
          />
        </div>
        <div class="q-mb-md">
          <q-badge
            color="info"
            :label="`${adminProperties.length} admin Properties`"
          />
        </div> -->
        <!-- <div class="properties-grid">
          <RealtyGameAdminPropertyCard
            v-for="(realtyGameListing, index) in adminProperties"
            :key="realtyGameListing.uuid"
            :property="realtyGameListing.listing_details"
            :realtyGameListing="realtyGameListing"
            :index="index"
            :realtyGameListingUuid="realtyGameListing.uuid"
            @update-title="
              (newTitle) =>
                updatePropertyTitle(realtyGameListing.listing_details, newTitle)
            "
            @update-visibility="
              (val) => updateGameListingVisibility(realtyGameListing, val)
            "
            @update-image-visibility="
              (image, isHidden) =>
                updateImageVisibility(
                  realtyGameListing.listing_details,
                  image,
                  isHidden
                )
            "
            @update-gl-attribute="
              (attribute, value) =>
                updateRealtyGameListingAttribute(
                  realtyGameListing,
                  attribute,
                  value
                )
            "
          />
        </div> -->
      </div>

      <!-- Empty State -->
      <div v-else class="text-center q-py-xl">
        <q-icon name="home" size="50px" color="grey-5" />
        <p class="q-mt-md text-grey-6">No properties found</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// import { ref, onMounted } from 'vue'
import { onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRealtyGame } from 'src/concerns/realty-game/composables/useRealtyGame'
import { useRoute } from 'vue-router'
import RealtyGameAdminPropertyCard from 'src/concerns/realty-game/admin-components/RealtyGameAdminPropertyCard.vue'
import RealtyGameDetailsAdminCard from 'src/concerns/realty-game/admin-components/RealtyGameDetailsAdminCard.vue'
//  '../components/RealtyGameAdminPropertyCard.vue'

const props = defineProps({
  realtyGameSummary: {
    type: Object,
  },
  // shareableResultsUrl: {
  //   type: String,
  //   required: true,
  // },
  // isCurrentUserSession: {
  //   type: Boolean,
  //   required: true,
  // },
})

const $q = useQuasar()
// Until a dedicated klavo create route exists, avoid linking to price-game routes
const showCreateComingSoon = () => {
  $q.notify({
    color: 'info',
    message: 'Klavo game creator is coming soon.',
    icon: 'info',
  })
}

// Initialize the price guess composable
const {
  adminProperties,
  isLoading,
  error,
  // properties below are the ones
  // visible to a regular user
  gameListings,
  fetchPriceGuessDataForAdmin,
  updateGameListingVisibility: apiUpdateGameListingVisibility,
  updatePropertyTitle: apiUpdatePropertyTitle,
  updateImageVisibility: apiUpdateImageVisibility,
  updateRealtyGameListingAttribute: apiUpdateRealtyGameListingAttribute,
} = useRealtyGame()

// Game details handled by component
const onGameDetailsSaved = () => {
  // Optional: refresh data or show additional UI feedback
  refreshData()
}

const $route = useRoute()

// // Local state for tracking updates
// const updatingProperties = ref(new Set())
// const updatingImages = ref(new Set())

// const adminGameListings = computed(() => {
//   let rgp = []
//   adminProperties.value.forEach(element => {
//     rgp.push(element.listing_details)
//   })
//   return rgp
// })
// Methods
const refreshData = async () => {
  try {
    await fetchPriceGuessDataForAdmin($route.params.gameSlug)
    $q.notify({
      color: 'positive',
      message: 'Properties refreshed successfully',
      icon: 'check',
    })
  } catch (err) {
    $q.notify({
      color: 'negative',
      message: 'Failed to refresh properties',
      icon: 'error',
    })
  }
}

const updateGameListingVisibility = async (realtyGameListing, newVisible) => {
  let property = realtyGameListing.listing_details
  property._updating = true
  const oldVisible = property.visible
  // console.log(props)
  property.visible = newVisible
  try {
    await apiUpdateGameListingVisibility(
      props.realtyGameSummary.uuid_for_game,
      realtyGameListing.uuid,
      property.visible
    )
    $q.notify({
      color: 'positive',
      message: `Property ${property.visible ? 'shown' : 'hidden'} in game`,
      icon: property.visible ? 'visibility' : 'visibility_off',
    })
  } catch (err) {
    property.visible = oldVisible
    $q.notify({
      color: 'negative',
      message: 'Failed to update property visibility',
      icon: 'error',
    })
  } finally {
    property._updating = false
  }
}

const updatePropertyTitle = async (property, newTitle) => {
  property._updating = true
  const oldTitle = property.title
  property.title = newTitle
  try {
    await apiUpdatePropertyTitle(property.uuid, property.title)
    $q.notify({
      color: 'positive',
      message: 'Property title updated',
      icon: 'edit',
    })
  } catch (err) {
    property.title = oldTitle
    $q.notify({
      color: 'negative',
      message: 'Failed to update property title',
      icon: 'error',
    })
  } finally {
    property._updating = false
  }
}

const updateImageVisibility = async (property, image, isHidden) => {
  image._updating = true

  try {
    // Update the flag
    image.flag_is_hidden = isHidden

    await apiUpdateImageVisibility(image.uuid, isHidden)

    $q.notify({
      color: 'positive',
      message: `Image ${isHidden ? 'hidden' : 'shown'} in game`,
      icon: isHidden ? 'visibility_off' : 'visibility',
    })
  } catch (err) {
    // Revert the change on error
    image.flag_is_hidden = !isHidden
    $q.notify({
      color: 'negative',
      message: 'Failed to update image visibility',
      icon: 'error',
    })
  } finally {
    image._updating = false
  }
}

const updateRealtyGameListingAttribute = async (
  realtyGameListing,
  attribute,
  value
) => {
  realtyGameListing._updating = true
  const oldValue = realtyGameListing[attribute]
  realtyGameListing[attribute] = value

  try {
    await apiUpdateRealtyGameListingAttribute(
      realtyGameListing.uuid,
      attribute,
      value
    )

    $q.notify({
      color: 'positive',
      message: `${attribute
        .replace('gl_', '')
        .replace('_atr', '')
        .replace('_', ' ')} updated`,
      icon: 'edit',
    })
  } catch (err) {
    realtyGameListing[attribute] = oldValue
    $q.notify({
      color: 'negative',
      message: `Failed to update ${attribute
        .replace('gl_', '')
        .replace('_atr', '')
        .replace('_', ' ')}`,
      icon: 'error',
    })
  } finally {
    realtyGameListing._updating = false
  }
}

// Initialize data on mount
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.price-guess-properties-admin {
  max-width: 1200px;
  margin: 0 auto;
}

.property-card {
  transition: opacity 0.3s ease;
}

.property-hidden {
  opacity: 0.6;
}

.property-header {
  display: flex;
  align-items: center;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}

.image-item {
  transition: opacity 0.3s ease;
}

.image-hidden {
  opacity: 0.5;
}

.image-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.property-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px;
}

.image-info {
  text-align: center;
}
</style>
