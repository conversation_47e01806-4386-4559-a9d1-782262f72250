<template>
  <div class="klavo-game-property-guesses-manage-page">
    <div class="max-ctr q-pa-md">
      <!-- Breadcrumbs -->
      <div class="q-mb-md">
        <q-breadcrumbs class="text-grey-7">
          <q-breadcrumbs-el
            :label="game?.title || 'Game'"
            :to="{ name: 'rKlavoGame', params: { gameSlug } }"
          />
          <q-breadcrumbs-el
            :label="listingInGame?.title || 'Property'"
            :to="{
              name: 'rKlavoGameProperty',
              params: { gameSlug: $route.params.gameSlug, listingInGameUuid: $route.params.listingInGameUuid },
              query: $route.query,
            }"
          />
          <q-breadcrumbs-el label="manage guesstimates" />
        </q-breadcrumbs>
      </div>

      <!-- Loading -->
      <div v-if="isLoading" class="text-center q-pa-xl">
        <q-spinner size="3em" color="primary" />
        <div class="q-mt-md text-h6">Loading guesstimates…</div>
      </div>

      <!-- Error -->
      <div v-else-if="error" class="text-center q-pa-xl">
        <q-icon name="error" color="negative" size="3em" />
        <div class="q-mt-md text-h6 text-negative">Failed to load guesstimates</div>
        <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
        <q-btn color="primary" label="Retry" @click="loadData" />
      </div>

      <div v-else-if="board" class="q-gutter-y-lg">
        <!-- Target Price Highlight -->
        <q-card flat bordered class="target-price-card">
          <q-card-section class="row items-center justify-between">
            <div>
              <div class="text-caption text-grey-7">Target Price</div>
              <div class="target-price-value">
                {{
                  formatCents(
                    listingInGame?.price_to_be_guessed_cents,
                    listingInGame?.currency
                  )
                }}
              </div>
              <div
                v-if="
                  listingInGame?.sale_listing_price_cents != null &&
                  listingInGame?.sale_listing_price_cents !==
                    listingInGame?.price_to_be_guessed_cents
                "
                class="text-caption text-grey-7"
              >
                Listing Price:
                {{
                  formatCents(
                    listingInGame.sale_listing_price_cents,
                    listingInGame.currency
                  )
                }}
              </div>
            </div>
            <div class="text-right">
              <div class="text-caption text-grey-7">Total Guesstimates</div>
              <div class="text-h6 q-mt-xs">
                {{ board.guesses_summary?.count_total_guesses || board.counts?.guesses || totalGuesses }}
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Summary Stats -->
        <q-card flat bordered>
          <q-card-section>
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-sm-4 col-md-3">
                <div class="text-subtitle2 text-grey-7 q-mb-xs">
                  Average Guesstimate
                </div>
                <div class="text-h6">
                  {{
                    board.guesses_summary?.average_guess_formatted ||
                      formatCents(
                        board.guesses_summary?.average_guess_cents,
                        listingInGame?.currency
                      )
                  }}
                </div>
                <div
                  v-if="
                    board.guesses_summary?.average_guess_vs_price_to_be_guessed_percentage !=
                    null
                  "
                  class="text-caption text-grey-7"
                >
                  {{
                    board.guesses_summary
                      ?.average_guess_vs_price_to_be_guessed_percentage
                  }}% vs target
                </div>
              </div>
              <div class="col-12 col-sm-4 col-md-3">
                <div class="text-subtitle2 text-grey-7 q-mb-xs">
                  Highest Guesstimate
                </div>
                <div class="text-h6">
                  {{
                    board.guesses_summary?.highest_guess_formatted ||
                      formatCents(
                        board.guesses_summary?.highest_guess_cents,
                        listingInGame?.currency
                      )
                  }}
                </div>
                <div
                  v-if="
                    board.guesses_summary?.highest_guess_vs_price_to_be_guessed_percentage !=
                    null
                  "
                  class="text-caption text-grey-7"
                >
                  {{
                    board.guesses_summary
                      ?.highest_guess_vs_price_to_be_guessed_percentage
                  }}% vs target
                </div>
              </div>
              <div class="col-12 col-sm-4 col-md-3">
                <div class="text-subtitle2 text-grey-7 q-mb-xs">
                  Lowest Guesstimate
                </div>
                <div class="text-h6">
                  {{
                    board.guesses_summary?.lowest_guess_formatted ||
                      formatCents(
                        board.guesses_summary?.lowest_guess_cents,
                        listingInGame?.currency
                      )
                  }}
                </div>
                <div
                  v-if="
                    board.guesses_summary?.lowest_guess_vs_price_to_be_guessed_percentage !=
                    null
                  "
                  class="text-caption text-grey-7"
                >
                  {{
                    board.guesses_summary
                      ?.lowest_guess_vs_price_to_be_guessed_percentage
                  }}% vs target
                </div>
              </div>
              <div class="col-12 col-sm-4 col-md-3">
                <div class="text-caption text-grey-7 q-mb-xs">
                  Total Sessions
                </div>
                <div class="text-h6">
                  {{ board.counts?.sessions || board.sessions?.length || 0 }}
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <div class="row items-center justify-between q-mt-xl">
          <div>
            <div class="text-h5 text-weight-medium">Manage Guesstimates</div>
            <div class="text-subtitle2 text-grey-7" v-if="listingInGame?.title">{{ listingInGame.title }}</div>
          </div>
          <div class="row q-gutter-sm">
            <q-btn
              :to="{
                name: 'rKlavoGamePropertyGuesses',
                params: { gameSlug: $route.params.gameSlug, listingInGameUuid: $route.params.listingInGameUuid },
                query: $route.query,
              }"
              color="primary"
              flat
              icon="list"
              label="View All"
            />
            <q-btn
              :to="{
                name: 'rKlavoGameProperty',
                params: { gameSlug: $route.params.gameSlug, listingInGameUuid: $route.params.listingInGameUuid },
                query: $route.query,
              }"
              color="primary"
              flat
              icon="home"
              label="Property"
            />
          </div>
        </div>

        <!-- Actions & Filters Bar -->
        <q-card flat bordered class="q-mt-sm">
          <q-card-section class="column q-gutter-md">
            <div class="row items-center justify-between q-gutter-sm">
              <div class="row items-center q-gutter-sm">
                <q-checkbox v-model="selectAll" :indeterminate="isIndeterminate" @update:model-value="toggleSelectAll" />
                <div class="text-caption">Select All ({{ selectedGuessUuids.size }} / {{ filteredGuesses.length }})</div>
                <q-separator vertical inset class="q-mx-sm" />
                <q-btn
                  color="negative"
                  unelevated
                  size="sm"
                  icon="delete"
                  :disable="!selectedGuessUuids.size || deleting"
                  :loading="deleting"
                  label="Delete Selected"
                  @click="confirmDeleteSelected"
                />
                <q-btn
                  color="warning"
                  outline
                  size="sm"
                  icon="refresh"
                  :disable="deleting || isLoading"
                  label="Refresh"
                  @click="loadData"
                />
              </div>
              <div class="text-caption text-grey-7" v-if="lastLoadedAt">Loaded {{ lastLoadedAt.toLocaleTimeString() }}</div>
            </div>
            <!-- Filters -->
            <div class="row q-col-gutter-sm items-end filter-grid">
              <div class="col-12 col-sm-3">
                <q-input v-model="filters.player" label="Player name" dense outlined clearable @keyup.enter="applyFilters" />
              </div>
              <div class="col-6 col-sm-2">
                <q-input v-model.number="filters.minScore" type="number" label="Min Score" dense outlined clearable />
              </div>
              <div class="col-6 col-sm-2">
                <q-input v-model.number="filters.maxScore" type="number" label="Max Score" dense outlined clearable />
              </div>
              <div class="col-6 col-sm-2">
                <q-input v-model.number="filters.minPct" type="number" label="Min % Off" dense outlined clearable />
              </div>
              <div class="col-6 col-sm-2">
                <q-input v-model.number="filters.maxPct" type="number" label="Max % Off" dense outlined clearable />
              </div>
              <div class="col-6 col-sm-3">
                <q-input v-model="filters.dateFrom" type="date" label="Date From" dense outlined clearable />
              </div>
              <div class="col-6 col-sm-3">
                <q-input v-model="filters.dateTo" type="date" label="Date To" dense outlined clearable />
              </div>
              <div class="col-12 col-sm-3">
                <q-select v-model="filters.sortKey" :options="sortOptions" label="Sort" dense outlined emit-value map-options />
              </div>
              <div class="col-12 col-sm-3 row items-center q-gutter-sm">
                <q-select v-model="perPage" :options="perPageOptions" label="Per Page" dense outlined emit-value map-options style="flex:1" />
                <q-btn size="sm" color="primary" outline icon="filter_alt" label="Apply" @click="applyFilters" />
                <q-btn size="sm" flat color="grey-7" icon="clear" @click="resetFilters" />
              </div>
            </div>
            <div class="row items-center q-gutter-sm">
              <div class="text-caption">
                Showing {{ paginatedGuesses.length }} of {{ filteredGuesses.length }} (Total raw: {{ totalGuesses }})
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Guesses / Sessions Table -->
        <q-card flat bordered>
          <q-card-section>
            <div class="row items-center justify-between q-mb-sm" v-if="totalPages > 1 || filteredGuesses.length">
              <div class="row items-center q-gutter-xs">
                <q-btn dense flat icon="chevron_left" @click="prevPage" :disable="page <= 1" />
                <q-btn dense flat icon="chevron_right" @click="nextPage" :disable="page >= totalPages" />
                <span class="text-caption">Page {{ page }} / {{ totalPages }}</span>
              </div>
              <q-pagination v-if="totalPages > 1" v-model="page" :max="totalPages" max-pages="7" size="sm" boundary-links />
            </div>
            <div class="table-wrapper">
              <table class="sessions-table">
                <thead>
                  <tr>
                    <th style="width:32px;">
                      <q-checkbox v-model="selectAll" :indeterminate="isIndeterminate" @update:model-value="toggleSelectAll" />
                    </th>
                    <th>Player</th>
                    <th>Date</th>
                    <th class="text-right cursor-pointer" @click="togglePriceSort" title="Sort by guesstimate">
                      Guesstimate
                      <q-icon
                        v-if="filters.sortKey.startsWith('price_')"
                        :name="filters.sortKey === 'price_desc' ? 'south' : 'north'"
                        size="14px"
                        class="q-ml-xs"
                      />
                    </th>
                    <th class="text-right">Score</th>
                    <th class="text-right">% Off</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="row in paginatedGuesses"
                    :key="row.guess.uuid"
                    :class="{ 'row-deleting': deletingGuesses.has(row.guess.uuid) }"
                  >
                    <td>
                      <q-checkbox v-model="guessSelections[row.guess.uuid]" @update:model-value="(val) => updateSelection(row.guess.uuid, val)" />
                    </td>
                    <td>{{ row.session.session_guest_name || 'Anonymous Player' }}</td>
                    <td class="text-no-wrap">{{ formatDateTime(row.guess.created_at) }}</td>
                    <td class="text-right">
                      {{
                        row.guess.formatted_guessed_price ||
                        formatAmountCurrency(
                          row.guess.guessed_price_amount_cents,
                          row.guess.guessed_price_currency
                        )
                      }}
                    </td>
                    <td class="text-right">
                      <span :class="'text-caption ' + scoreColor(row.guess.score_for_guess)">
                        {{ row.guess.score_for_guess ?? '—' }}
                      </span>
                    </td>
                    <td class="text-right">
                      <span>
                        {{
                          row.guess.percentage_above_or_below != null
                            ? row.guess.percentage_above_or_below + '%'
                            : '—'
                        }}
                      </span>
                    </td>
                  </tr>
                  <tr v-if="!paginatedGuesses.length && !isLoading">
                    <td colspan="6" class="text-center text-grey-6 q-pa-md">
                      No guesses match filters
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div v-else class="text-center q-pa-xl">
        <q-icon name="search_off" color="grey-5" size="3em" />
        <div class="q-mt-md text-h6 text-grey-7">No data available</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'
import { useQuasar } from 'quasar'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

const props = defineProps({
  gameSlug: { type: String, required: false },
  listingInGameUuid: { type: String, required: true }
})
const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()
// Initialize pagination from query
const perPage = ref(Number($route.query.gpp) || 50)
const page = ref(Number($route.query.gp) || 1)
const gameSlug = computed(() => props.gameSlug || $route.params.gameSlug)
const propertyUuid = computed(() => props.listingInGameUuid)

const isLoading = ref(false)
const error = ref('')
const board = ref(null)
const lastLoadedAt = ref(null)

const filters = ref({
  player: ($route.query.gpl || '').toString(),
  minScore: $route.query.gmins ? Number($route.query.gmins) : null,
  maxScore: $route.query.gmaxs ? Number($route.query.gmaxs) : null,
  minPct: $route.query.gminp ? Number($route.query.gminp) : null,
  maxPct: $route.query.gmaxp ? Number($route.query.gmaxp) : null,
  dateFrom: ($route.query.gdf || '').toString(),
  dateTo: ($route.query.gdt || '').toString(),
  sortKey: ($route.query.gsrt || 'date_desc').toString()
})
const deleting = ref(false)
const deletingGuesses = ref(new Set())
const selectedGuessUuids = ref(new Set())
const guessSelections = ref({})
const game = computed(() => board.value?.game)
// Provide listingInGame for template (was missing, causing Vue warn)
const listingInGame = computed(() => board.value?.listing_in_game || board.value?.listingInGame || null)

function mapSortForServer() {
  const sk = filters.value.sortKey || 'date_desc'
  const [field, dir] = sk.split('_')
  const supportedFields = ['name', 'date', 'price']
  const sortField = supportedFields.includes(field) ? field : 'date'
  const sortDir = dir === 'asc' ? 'asc' : 'desc'
  return { sortField, sortDir }
}

// Flatten guesses for filtering & (client) filtering after server pagination
const allGuesses = computed(() => {
  if (!board.value?.sessions) return []
  const rows = []
  board.value.sessions.forEach(session => {
    ;(session.guesses || []).forEach(guess => rows.push({ session, guess }))
  })
  return rows
})
const totalGuesses = computed(() => allGuesses.value.length)
const perPageOptions = [25, 50, 100, 250].map(n => ({ label: String(n), value: n }))
const sortOptions = [
  { label: 'Date (Newest)', value: 'date_desc' },
  { label: 'Date (Oldest)', value: 'date_asc' },
  { label: 'Guesstimate (High)', value: 'price_desc' },
  { label: 'Guesstimate (Low)', value: 'price_asc' },
  { label: 'Score (High)', value: 'score_desc' },
  { label: 'Score (Low)', value: 'score_asc' },
  { label: '% Off (Low)', value: 'pct_asc' },
  { label: '% Off (High)', value: 'pct_desc' }
]

const filteredGuesses = computed(() => {
  let rows = allGuesses.value
  const f = filters.value
  if (f.player) {
    const p = f.player.toLowerCase()
    rows = rows.filter(r => (r.session.session_guest_name || 'anonymous').toLowerCase().includes(p))
  }
  if (f.minScore != null) rows = rows.filter(r => (r.guess.score_for_guess ?? -Infinity) >= f.minScore)
  if (f.maxScore != null) rows = rows.filter(r => (r.guess.score_for_guess ?? Infinity) <= f.maxScore)
  if (f.minPct != null) rows = rows.filter(r => (r.guess.percentage_above_or_below ?? Infinity) >= f.minPct)
  if (f.maxPct != null) rows = rows.filter(r => (r.guess.percentage_above_or_below ?? -Infinity) <= f.maxPct)
  if (f.dateFrom) {
    const fromTs = new Date(f.dateFrom + 'T00:00:00').getTime()
    rows = rows.filter(r => new Date(r.guess.created_at).getTime() >= fromTs)
  }
  if (f.dateTo) {
    const toTs = new Date(f.dateTo + 'T23:59:59').getTime()
    rows = rows.filter(r => new Date(r.guess.created_at).getTime() <= toTs)
  }
  // Sorting
  rows = [...rows]
  switch (f.sortKey) {
    case 'date_asc':
      rows.sort((a, b) => new Date(a.guess.created_at) - new Date(b.guess.created_at))
      break
    case 'date_desc':
      rows.sort((a, b) => new Date(b.guess.created_at) - new Date(a.guess.created_at))
      break
    case 'score_desc':
      rows.sort((a, b) => (b.guess.score_for_guess ?? -Infinity) - (a.guess.score_for_guess ?? -Infinity))
      break
    case 'score_asc':
      rows.sort((a, b) => (a.guess.score_for_guess ?? Infinity) - (b.guess.score_for_guess ?? Infinity))
      break
    case 'price_desc':
      rows.sort((a, b) => (b.guess.guessed_price_amount_cents ?? -Infinity) - (a.guess.guessed_price_amount_cents ?? -Infinity))
      break
    case 'price_asc':
      rows.sort((a, b) => (a.guess.guessed_price_amount_cents ?? Infinity) - (b.guess.guessed_price_amount_cents ?? Infinity))
      break
    case 'pct_asc':
      rows.sort((a, b) => (a.guess.percentage_above_or_below ?? Infinity) - (b.guess.percentage_above_or_below ?? Infinity))
      break
    case 'pct_desc':
      rows.sort((a, b) => (b.guess.percentage_above_or_below ?? -Infinity) - (a.guess.percentage_above_or_below ?? -Infinity))
      break
  }
  return rows
})

// Use server-provided total pages; fallback to 1
const totalPages = computed(
  () => board.value?.sessions_pagination?.total_pages || 1
)
// After server pagination we already have just one page of sessions; apply client filters
const paginatedGuesses = computed(() => filteredGuesses.value)

function applyFilters() {
  page.value = 1
  pushQueryState()
  loadData()
}
function resetFilters() {
  filters.value = { player: '', minScore: null, maxScore: null, minPct: null, maxPct: null, dateFrom: '', dateTo: '', sortKey: 'date_desc' }
  page.value = 1
  pushQueryState()
  loadData()
}
function nextPage() {
  if (page.value < totalPages.value) {
    page.value++
  }
}
function prevPage() {
  if (page.value > 1) {
    page.value--
  }
}
function togglePriceSort() {
  if (filters.value.sortKey === 'price_desc') {
    filters.value.sortKey = 'price_asc'
  } else if (filters.value.sortKey === 'price_asc') {
    filters.value.sortKey = 'price_desc'
  } else {
    filters.value.sortKey = 'price_desc'
  }
  applyFilters()
}
const suppressPageWatch = ref(false)
watch([() => filters.value.sortKey, perPage], () => {
  page.value = 1
  pushQueryState()
  loadData()
})
watch(page, (n, o) => {
  if (n === o) return
  if (suppressPageWatch.value) {
    suppressPageWatch.value = false
    return
  }
  pushQueryState()
  loadData()
})

function pushQueryState() {
  const q = { ...$route.query }
  q.gp = String(page.value)
  q.gpp = String(perPage.value)
  q.gsrt = filters.value.sortKey
  if (filters.value.player) q.gpl = filters.value.player; else delete q.gpl
  if (filters.value.minScore != null) q.gmins = String(filters.value.minScore); else delete q.gmins
  if (filters.value.maxScore != null) q.gmaxs = String(filters.value.maxScore); else delete q.gmaxs
  if (filters.value.minPct != null) q.gminp = String(filters.value.minPct); else delete q.gminp
  if (filters.value.maxPct != null) q.gmaxp = String(filters.value.maxPct); else delete q.gmaxp
  if (filters.value.dateFrom) q.gdf = filters.value.dateFrom; else delete q.gdf
  if (filters.value.dateTo) q.gdt = filters.value.dateTo; else delete q.gdt
  $router.replace({ query: q })
}

const formatAmountCurrency = (cents, currency) => {
  if (cents == null) return '-'
  const amt = cents / 100
  try {
    return new Intl.NumberFormat(undefined, { style: 'currency', currency: currency || listingInGame.value?.currency || 'USD' }).format(amt)
  } catch {
    return `${amt.toLocaleString()} ${currency || ''}`.trim()
  }
}
const formatCents = (cents, currency = 'USD') => {
  if (cents == null) return '-'
  const amt = cents / 100
  try {
    return new Intl.NumberFormat(undefined, {
      style: 'currency',
      currency: currency || listingInGame.value?.currency || 'USD'
    }).format(amt)
  } catch {
    return `${amt.toLocaleString()} ${currency || ''}`.trim()
  }
}

const scoreColor = (score) => {
  if (score >= 80) return 'text-positive'
  if (score >= 50) return 'text-info'
  if (score >= 30) return 'text-warning'
  return 'text-negative'
}

const formatDateTime = (iso) => iso ? new Date(iso).toLocaleString() : '-'

const loadData = async () => {
  isLoading.value = true
  error.value = ''
  try {
    const { sortField, sortDir } = mapSortForServer()
    const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/listing_results_board/${gameSlug.value}/${propertyUuid.value}`
    const { data } = await axios.get(apiUrl, {
      withCredentials: false,
      params: {
        sessions_page: page.value,
        sessions_per_page: perPage.value,
        sessions_sort: sortField,
        sessions_dir: sortDir
      }
    })
    board.value = data
    // Sync page with server if different (eg out-of-range correction)
    const srvPage = data?.sessions_pagination?.page
    if (srvPage && srvPage !== page.value) {
      suppressPageWatch.value = true
      page.value = srvPage
    }
    lastLoadedAt.value = new Date()
    rebuildSelections()
  } catch (e) {
    error.value = e?.response?.data?.message || e?.message || 'Unknown error'
  } finally {
    isLoading.value = false
  }
}

function rebuildSelections() {
  const newSelections = {}
  allGuesses.value.forEach(r => { newSelections[r.guess.uuid] = selectedGuessUuids.value.has(r.guess.uuid) })
  guessSelections.value = newSelections
}

const selectAll = ref(false)
const isIndeterminate = computed(() => selectedGuessUuids.value.size > 0 && selectedGuessUuids.value.size < totalGuesses.value)

function toggleSelectAll(val) {
  if (val) {
    selectedGuessUuids.value = new Set(filteredGuesses.value.map(r => r.guess.uuid))
  } else {
    selectedGuessUuids.value = new Set()
  }
  rebuildSelections()
}

function updateSelection(uuid, val) {
  if (val) selectedGuessUuids.value.add(uuid)
  else selectedGuessUuids.value.delete(uuid)
  selectAll.value = selectedGuessUuids.value.size === totalGuesses.value && totalGuesses.value > 0
}

function confirmDeleteSelected() {
  if (!selectedGuessUuids.value.size) return
  $q.dialog({
    title: 'Delete Guesses',
    message: `Are you sure you want to delete ${selectedGuessUuids.value.size} selected guesstimate(s)? This cannot be undone.`,
    cancel: true,
    persistent: true
  }).onOk(() => deleteSelected())
}

async function deleteSelected() {
  deleting.value = true
  try {
    // Optimistic UI: mark rows
    deletingGuesses.value = new Set(selectedGuessUuids.value)
    const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/scoot_games_mgmt/listing_guess/${gameSlug.value}/${propertyUuid.value}`
    // Send in batches if large
    const uuids = Array.from(selectedGuessUuids.value)
    const BATCH_SIZE = 50
    for (let i = 0; i < uuids.length; i += BATCH_SIZE) {
      const batch = uuids.slice(i, i + BATCH_SIZE)
      await axios.delete(apiUrl, { data: { guess_uuids: batch } })
    }
    $q.notify({ color: 'positive', message: 'Selected guesstimates deleted', icon: 'check' })
    selectedGuessUuids.value.clear()
    await loadData()
  } catch (e) {
    console.error(e)
    $q.notify({ color: 'negative', message: e?.response?.data?.message || 'Failed to delete guesses', icon: 'error' })
  } finally {
    deleting.value = false
    deletingGuesses.value.clear()
  }
}

onMounted(loadData)
</script>

<style scoped>
.max-ctr { max-width: 1100px; margin: 0 auto; }
.table-wrapper { overflow-x: auto; }
.sessions-table { width: 100%; border-collapse: collapse; font-size: 0.9rem; }
.sessions-table th, .sessions-table td { padding: 6px 8px; border-bottom: 1px solid #ececec; vertical-align: middle; white-space: nowrap; }
.sessions-table th { background: #fafafa; font-weight: 600; text-align: left; user-select: none; }
.sessions-table th.text-right, .sessions-table td.text-right { text-align: right; }
.row-deleting { opacity: 0.4; }
.target-price-card { border-left: 6px solid var(--q-primary); }
.target-price-value { font-size: 1.5rem; font-weight: 600; line-height: 1.1; }
</style>
