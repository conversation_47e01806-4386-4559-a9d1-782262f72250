<template>
  <q-layout view="lhh LpR ffr" class="hpg-main-layout-outer">
    <q-header class="hpg-main-mht-ctr bg-white" reveal elevated>
      <q-toolbar class="hpg-main-marketing-header-toolbar container max-ctr">
        <q-toolbar-title class="inline-flex items-center">
          <a href="https://cameron.housepriceguess.com/" class="logo-link q-mr-sm">
            <img
              :src="logoUrl"
              alt="<PERSON> - SPACE Real Estate"
              class="site-logo hpg-logo-img"
              style="height: 45px; padding: 0px"
            />
          </a>
          <span class="text-h6 text-primary no-wrap"><PERSON>'s Price Challenge</span>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container
      :class="[`main-layout-htoc-2024g-gate`, maxCtrClass]"
      style="padding-top: 50px"
    >
      <router-view
        @blurbCta="blurbCta"
        :isPriceGuessOnly="true"
        @showNotification="showNotification"
        :whitelabelNameDisplay="whitelabelNameDisplay"
        :serviceEmail="serviceEmail"
        :gameTitle="gameTitle"
        :gameDesc="gameDesc"
        :gameListings="gameListings"
        :isLoading="isLoading"
        :gameDefaultCurrency="gameDefaultCurrency"
        :firstPropListing="firstPropListing"
        :totalProperties="totalProperties"
        :realtyGameSummary="realtyGameSummary"
        :gameCommunitiesDetails="gameCommunitiesDetails"
      />
    </q-page-container>
    <div class="ajax-bar-container">
      <q-ajax-bar
        ref="bar"
        position="top"
        color="accent"
        size="10px"
        :skip-hijack="false"
        :hijack-filter="loadingBarFilterFn"
      />
    </div>
    <StarTeamFooter
      homeUrl="https://housepriceguess.com/"
      :whitelabelNameDisplay="whitelabelNameDisplay"
      :serviceEmail="serviceEmail"
    ></StarTeamFooter>
  </q-layout>
</template>

<script>
import StarTeamFooter from 'src/concerns/branded-game/components/head-foot/StarTeamFooter.vue'
import { defineComponent, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router'
import useJsonLd from 'src/compose/useJsonLd.js'
import { useMeta } from 'quasar'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
import { storeToRefs } from 'pinia'
import { useBrandedGame } from 'src/concerns/realty-game/composables/useBrandedGame'

// Import logo image (you can replace with Cameron's actual logo)
import logoUrl from '/icons/favicon-128x128.png'

export default defineComponent({
  name: 'CameronLayout',
  components: {
    StarTeamFooter,
  },
  data() {
    return {
      showNewAccEnqPrompt: false,
      selectedAccountPlan: 'free',
    }
  },
  props: {
    serviceEmail: {
      type: String,
      default: '<EMAIL>',
    },
    whitelabelNameDisplay: {
      type: String,
      default: 'Cameron Zatz-Krecker',
    },
  },
  computed: {
    showMobileFooter() {
      return this.$q.platform.is.mobile
    },
    maxCtrClass() {
      if (this.$route.name === 'rSubdomainRoot') {
        return ''
      } else {
        return 'max-ctr'
      }
    },
  },
  methods: {
    blurbCta(selectedAccountPlan) {
      this.selectedAccountPlan = selectedAccountPlan
      this.showNewAccEnqPrompt = true
    },
    blurbCtaEnd() {
      this.showNewAccEnqPrompt = false
    },
  },
  setup() {
    const $q = useQuasar()
    const route = useRoute()
    const brandedGameSlug = 'arizona-house-price-guess'

    // Initialize JSON-LD functionality
    const { initializeDefaultJsonLd, updateWebPageSchema, jsonLdScriptTags } =
      useJsonLd()

    // Initialize JSON-LD
    initializeDefaultJsonLd()

    // Update webpage schema based on route
    const routeMeta = route.meta || {}
    updateWebPageSchema({
      title: routeMeta.title,
      description: routeMeta.description,
      keywords: routeMeta.keywords,
    })

    // Meta management from store
    const metaStore = useRealtyGameMetaStore()
    const { title, description, image, url, keywords } = storeToRefs(metaStore)
    useMeta(() => ({
      title: title.value,
      meta: {
        description: { name: 'description', content: description.value },
        keywords: { name: 'keywords', content: keywords.value },
        'og:title': { property: 'og:title', content: title.value },
        'og:description': {
          property: 'og:description',
          content: description.value,
        },
        'og:image': { property: 'og:image', content: image.value },
        'og:url': { property: 'og:url', content: url.value },
        'og:type': { property: 'og:type', content: 'website' },
        'twitter:card': {
          name: 'twitter:card',
          content: 'summary_large_image',
        },
        'twitter:title': { name: 'twitter:title', content: title.value },
        'twitter:description': {
          name: 'twitter:description',
          content: description.value,
        },
        'twitter:image': { name: 'twitter:image', content: image.value },
        'twitter:url': { name: 'twitter:url', content: url.value },
      },
      link: {
        'preconnect-img': {
          rel: 'preconnect',
          href: 'https://images.unsplash.com',
          crossorigin: '',
        },
        'preconnect-api': {
          rel: 'preconnect',
          href: 'http://hpg-scoot.lvh.me:3333',
        },
        'preconnect-fonts': {
          rel: 'preconnect',
          href: 'https://fonts.googleapis.com',
        },
        'preconnect-fonts-gstatic': {
          rel: 'preconnect',
          href: 'https://fonts.gstatic.com',
          crossorigin: '',
        },
      },
      script: jsonLdScriptTags.value.map((item) => ({
        type: 'application/ld+json',
        id: item.id,
        innerHTML: item.json,
      })),
    }))

    function showNotification(notificationMessage) {
      $q.notify(notificationMessage)
    }

    // Branded Game Data Fetching
    const {
      gameTitle,
      gameDesc,
      gameListings,
      isLoading,
      fetchBrandedGameData,
      isRedditRoute,
      gameDefaultCurrency,
      firstPropListing,
      totalProperties,
      realtyGameSummary,
      gameCommunitiesDetails,
    } = useBrandedGame()

    const loadBrandedGame = async () => {
      try {
        const isReddit = isRedditRoute(route.name)
        await fetchBrandedGameData(brandedGameSlug, isReddit)
      } catch (e) {
        $q.notify({
          color: 'negative',
          message: 'Failed to load property data',
          icon: 'error',
        })
      }
    }

    onMounted(() => {
      loadBrandedGame()

      // Load FUB Widget Tracker (Follow Up Boss)
      if (!window.widgetTracker) {
        const fubScript = document.createElement('script')
        fubScript.innerHTML = `
          (function(w,i,d,g,e,t){w["WidgetTrackerObject"]=g;(w[g]=w[g]||function()
          {(w[g].q=w[g].q||[]).push(arguments);}),(w[g].ds=1*new Date());(e="script"),
          (t=d.createElement(e)),(e=d.getElementsByTagName(e)[0]);t.async=1;t.src=i;
          e.parentNode.insertBefore(t,e);})
          (window," https://widgetbe.com/agent",document,"widgetTracker");
          window.widgetTracker("create", "WT-GPZZIZJZ");
          window.widgetTracker("send", "pageview");
        `
        document.head.appendChild(fubScript)
      }
    })

    return {
      showNotification,
      loadingBarFilterFn(url) {
        return !(url.includes('prop_ev_init') || !url.includes('user'))
      },
      logoUrl,
      gameTitle,
      gameDesc,
      gameListings,
      isLoading,
      gameDefaultCurrency,
      firstPropListing,
      totalProperties,
      realtyGameSummary,
      gameCommunitiesDetails,
    }
  },
})
</script>

<style scoped>
.main-layout-hpg-main-2024g {
  padding-top: 50px;
}
.hpg-logo-img {
  height: 40px;
  width: auto;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  background: #fff;
  padding: 4px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.hpg-logo-img:hover {
  transform: scale(1.05) rotate(-2deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
@media (max-width: 768px) {
  .hpg-logo-img {
    height: 32px;
    padding: 3px;
  }
}
.site-logo {
  height: 32px;
  width: auto;
  vertical-align: middle;
}
.logo-link {
  display: inline-block;
  margin-right: 12px;
}
.inline-flex {
  display: inline-flex;
  align-items: center;
}
.no-wrap {
  white-space: nowrap;
}
</style>
