<template>
  <q-layout view="lhh LpR ffr" class="hpg-main-layout-outer valencia-property-layout">
    <q-header class="hpg-main-mht-ctr valencia-header" reveal elevated>
      <q-toolbar class="hpg-main-marketing-header-toolbar container max-ctr">
        <q-toolbar-title class="inline-flex items-center">
          <a href="https://valencia.housepriceguess.com/" class="logo-link q-mr-sm">
            <img
              :src="logoUrl"
              alt="Graham Hunt - Valencia Property"
              class="site-logo valencia-logo"
              style="height: 55px; padding: 0px"
            />
          </a>
          <span class="text-h6 valencia-title no-wrap">Graham Hunt - Valencia Property</span>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container
      :class="[`main-layout-htoc-2024g-gate`, maxCtrClass]"
      style="padding-top: 50px"
    >
      <router-view
        @blurbCta="blurbCta"
        :isPriceGuessOnly="true"
        @showNotification="showNotification"
        :whitelabelNameDisplay="whitelabelNameDisplay"
        :serviceEmail="serviceEmail"
        :gameTitle="gameTitle"
        :gameDesc="gameDesc"
        :gameListings="gameListings"
        :isLoading="isLoading"
        :gameDefaultCurrency="gameDefaultCurrency"
        :firstPropListing="firstPropListing"
        :totalProperties="totalProperties"
        :realtyGameSummary="realtyGameSummary"
        :gameCommunitiesDetails="gameCommunitiesDetails"
      />
    </q-page-container>
    <div class="ajax-bar-container">
      <q-ajax-bar
        ref="bar"
        position="top"
        color="orange"
        size="10px"
        :skip-hijack="false"
        :hijack-filter="loadingBarFilterFn"
      />
    </div>
    <StarTeamFooter
      homeUrl="https://housepriceguess.com/"
      :whitelabelNameDisplay="whitelabelNameDisplay"
      :serviceEmail="serviceEmail"
    ></StarTeamFooter>
  </q-layout>
</template>

<script>
import StarTeamFooter from 'src/concerns/branded-game/components/head-foot/StarTeamFooter.vue'
import { defineComponent, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router'
import useJsonLd from 'src/compose/useJsonLd.js'
import { useMeta } from 'quasar'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
import { storeToRefs } from 'pinia'
import { useBrandedGame } from 'src/concerns/realty-game/composables/useBrandedGame'

// Import Valencia Property logo
import logoUrl from '/images/vplogo.png'

export default defineComponent({
  name: 'ValenciaLayout',
  components: {
    StarTeamFooter,
  },
  data() {
    return {
      showNewAccEnqPrompt: false,
      selectedAccountPlan: 'free',
    }
  },
  props: {
    serviceEmail: {
      type: String,
      default: '<EMAIL>',
    },
    whitelabelNameDisplay: {
      type: String,
      default: 'Graham Hunt - Valencia Property',
    },
  },
  computed: {
    showMobileFooter() {
      return this.$q.platform.is.mobile
    },
    maxCtrClass() {
      if (this.$route.name === 'rSubdomainRoot') {
        return ''
      } else {
        return 'max-ctr'
      }
    },
  },
  methods: {
    blurbCta(selectedAccountPlan) {
      this.selectedAccountPlan = selectedAccountPlan
      this.showNewAccEnqPrompt = true
    },
    blurbCtaEnd() {
      this.showNewAccEnqPrompt = false
    },
  },
  setup() {
    const $q = useQuasar()
    const route = useRoute()
    const brandedGameSlug = 'valencia-house-prices-game'

    // Initialize JSON-LD functionality
    const { initializeDefaultJsonLd, updateWebPageSchema, jsonLdScriptTags } =
      useJsonLd()

    // Initialize JSON-LD
    initializeDefaultJsonLd()

    // Update webpage schema based on route
    const routeMeta = route.meta || {}
    updateWebPageSchema({
      title: routeMeta.title,
      description: routeMeta.description,
      keywords: routeMeta.keywords,
    })

    // Meta management from store
    const metaStore = useRealtyGameMetaStore()
    const { title, description, image, url, keywords } = storeToRefs(metaStore)
    useMeta(() => ({
      title: title.value,
      meta: {
        description: { name: 'description', content: description.value },
        keywords: { name: 'keywords', content: keywords.value },
        'og:title': { property: 'og:title', content: title.value },
        'og:description': {
          property: 'og:description',
          content: description.value,
        },
        'og:image': { property: 'og:image', content: image.value },
        'og:url': { property: 'og:url', content: url.value },
        'og:type': { property: 'og:type', content: 'website' },
        'twitter:card': {
          name: 'twitter:card',
          content: 'summary_large_image',
        },
        'twitter:title': { name: 'twitter:title', content: title.value },
        'twitter:description': {
          name: 'twitter:description',
          content: description.value,
        },
        'twitter:image': { name: 'twitter:image', content: image.value },
        'twitter:url': { name: 'twitter:url', content: url.value },
      },
      link: {
        'preconnect-img': {
          rel: 'preconnect',
          href: 'https://images.unsplash.com',
          crossorigin: '',
        },
        'preconnect-api': {
          rel: 'preconnect',
          href: 'http://hpg-scoot.lvh.me:3333',
        },
        'preconnect-fonts': {
          rel: 'preconnect',
          href: 'https://fonts.googleapis.com',
        },
        'preconnect-fonts-gstatic': {
          rel: 'preconnect',
          href: 'https://fonts.gstatic.com',
          crossorigin: '',
        },
      },
      script: jsonLdScriptTags.value.map((item) => ({
        type: 'application/ld+json',
        id: item.id,
        innerHTML: item.json,
      })),
    }))

    function showNotification(notificationMessage) {
      $q.notify(notificationMessage)
    }

    // Branded Game Data Fetching
    const {
      gameTitle,
      gameDesc,
      gameListings,
      isLoading,
      fetchBrandedGameData,
      isRedditRoute,
      gameDefaultCurrency,
      firstPropListing,
      totalProperties,
      realtyGameSummary,
      gameCommunitiesDetails,
    } = useBrandedGame()

    const loadBrandedGame = async () => {
      try {
        const isReddit = isRedditRoute(route.name)
        await fetchBrandedGameData(brandedGameSlug, isReddit)
      } catch (e) {
        $q.notify({
          color: 'negative',
          message: 'Failed to load property data',
          icon: 'error',
        })
      }
    }

    onMounted(() => {
      loadBrandedGame()
    })

    return {
      showNotification,
      loadingBarFilterFn(url) {
        return !(url.includes('prop_ev_init') || !url.includes('user'))
      },
      logoUrl,
      gameTitle,
      gameDesc,
      gameListings,
      isLoading,
      gameDefaultCurrency,
      firstPropListing,
      totalProperties,
      realtyGameSummary,
      gameCommunitiesDetails,
    }
  },
})
</script>

<style scoped>
/* Valencia Property Branding - Graham's Bootstrap Colors */
.valencia-property-layout {
  /* Bootstrap Color System from Graham's Website */
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-primary-rgb: 13, 110, 253;
  --bs-secondary-rgb: 108, 117, 125;
  --bs-success-rgb: 25, 135, 84;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 220, 53, 69;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-rgb: 33, 37, 41;
  --bs-body-color: #212529;
  --bs-body-bg: #fff;

  /* Orange accent colors from Graham's site */
  --valencia-orange: #BB6D41;
  --valencia-orange-bright: #fd7e14;
  --valencia-orange-rgb: 187, 109, 65;

  /* Valencia-specific mappings using Bootstrap colors */
  --valencia-primary: var(--bs-primary);
  --valencia-dark: var(--bs-dark);
  --valencia-accent: var(--valencia-orange);
  --valencia-accent-bright: var(--valencia-orange-bright);
  --valencia-light: var(--bs-light);
  --valencia-light-bg: var(--bs-gray-100);
  --valencia-border: var(--bs-gray-300);

  /* Body styling from Graham's site */
  color: #465665;
}

.main-layout-hpg-main-2024g {
  padding-top: 50px;
}

/* Header Styling */
.valencia-header {
  background: var(--bs-dark) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.valencia-header .q-toolbar {
  background: var(--bs-dark);
}

/* Logo Styling */
.valencia-logo {
  height: 55px;
  width: auto;
  max-width: 100%;
  border-radius: 4px;
  background: transparent;
  padding: 4px 8px;
  transition: all 0.3s ease;
  opacity: 0.95;
  filter: brightness(1.1);
}

.valencia-logo:hover {
  transform: scale(1.05);
  opacity: 1;
  filter: brightness(1.2) drop-shadow(0 0 8px rgba(var(--valencia-orange-rgb), 0.6));
}

/* Title Styling */
.valencia-title {
  color: white !important;
  font-weight: 600;
  letter-spacing: 0.3px;
}

@media (max-width: 768px) {
  .valencia-logo {
    height: 45px;
    padding: 3px 6px;
  }
  .valencia-title {
    font-size: 0.95rem;
  }
}

.site-logo {
  height: 32px;
  width: auto;
  vertical-align: middle;
}

.logo-link {
  display: inline-block;
  margin-right: 12px;
}

.inline-flex {
  display: inline-flex;
  align-items: center;
}

.no-wrap {
  white-space: nowrap;
}
</style>
