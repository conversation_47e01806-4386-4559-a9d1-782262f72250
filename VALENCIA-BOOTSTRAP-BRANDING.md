# Valencia Subdomain - Bootstrap Branding Update

## Overview
Updated the Valencia subdomain styling to match <PERSON>'s main website color scheme, which uses the Bootstrap color system with prominent orange accent colors (#BB6D41 and #fd7e14).

## Updated Files

### 1. `/src/concerns/valencia/layouts/ValenciaLayout.vue`

**Changes:**
- Replaced custom Valencia color variables with Bootstrap's complete color system
- Added all Bootstrap CSS variables (primary, secondary, success, info, warning, danger, etc.)
- Added Bootstrap grayscale palette (gray-100 through gray-900)
- Added Bootstrap RGB color variables for rgba() support
- **Added orange accent color variables:**
  - `--valencia-orange: #BB6D41` (primary orange from <PERSON>'s site)
  - `--valencia-orange-bright: #fd7e14` (Bootstrap orange)
  - `--valencia-orange-rgb: 187, 109, 65` (for rgba usage)
- Mapped Valencia-specific variables to Bootstrap colors:
  - `--valencia-primary` → `--bs-primary` (#0d6efd - Bootstrap blue)
  - `--valencia-dark` → `--bs-dark` (#212529 - Bootstrap dark)
  - `--valencia-accent` → `--valencia-orange` (#BB6D41 - Orange accent)
  - `--valencia-accent-bright` → `--valencia-orange-bright` (#fd7e14)
  - `--valencia-light` → `--bs-light` (#f8f9fa)
  - `--valencia-light-bg` → `--bs-gray-100` (#f8f9fa)
  - `--valencia-border` → `--bs-gray-300` (#dee2e6)
- **Removed transparency from header:** Changed from `rgba()` to solid `var(--bs-dark)` background
- **Logo styling updated to blend with dark header:**
  - Removed white background (now transparent)
  - Added brightness filter for visibility
  - Hover effect with orange glow using drop-shadow
- Changed ajax loading bar color to orange
- Added body text color styling (#465665) from Graham's site

### 2. `/src/concerns/valencia/pages/ValenciaHomePage2025.vue`

**Changes:**
- Replaced custom Valencia color variables with Bootstrap's complete color system
- Added all Bootstrap CSS variables in `:root` selector
- **Added orange accent color variables** (same as layout file)
- Added Bootstrap font system variables:
  - `--bs-font-sans-serif`: System font stack
  - `--bs-font-monospace`: Monospace font stack
- Added Bootstrap typography variables:
  - Font family, size, weight, line-height, and color
- Added Fancybox button styling variables from Graham's site
- **Main title and subtitles updated to use orange:**
  - "Graham Hunt's Valencia Property Price Challenge": `var(--valencia-orange)`
  - Subtitle text: Orange color inline style
  - Hero kicker: Changed to `var(--valencia-orange)`
- **Updated section headings to use orange accent:**
  - "How it works" heading: `var(--valencia-orange)`
  - "This week's properties" heading: `var(--valencia-orange)`
  - "How it helps you" heading: `var(--valencia-orange)`
- **Changed all icons from blue/green to orange:**
  - Step icons: Changed from `color="primary"` to `color="orange"`
  - Benefit icons: Changed from `color="positive"` to `color="orange"`
- **Added orange accent styling:**
  - Step titles: Added `color: var(--valencia-orange)`
  - Benefit titles: Added `color: var(--valencia-orange)`
  - Step cards: Added 3px orange top border with hover effects
  - Benefit cards: Added 3px orange left border with hover effects
  - Hero bullets: Added 3px orange left border
  - Main title hover: Glows with orange and bright orange on hover
  - Link hover states: Bright orange color transition
- Updated scoped CSS classes to use Bootstrap variables:
  - Hero card background gradient
  - Hero kicker color
  - Hero subtitle color
  - Hero bullets border and background
  - Step cards border and background
  - Step and benefit text colors
  - Benefits card and grid styling

## Bootstrap Color Palette

### Primary Colors
- **Primary (Blue)**: #0d6efd
- **Secondary (Gray)**: #6c757d
- **Success (Green)**: #198754
- **Info (Cyan)**: #0dcaf0
- **Warning (Yellow)**: #ffc107
- **Danger (Red)**: #dc3545
- **Light**: #f8f9fa
- **Dark**: #212529

### Grayscale
- **Gray-100**: #f8f9fa (lightest)
- **Gray-200**: #e9ecef
- **Gray-300**: #dee2e6
- **Gray-400**: #ced4da
- **Gray-500**: #adb5bd
- **Gray-600**: #6c757d
- **Gray-700**: #495057
- **Gray-800**: #343a40
- **Gray-900**: #212529 (darkest)

### Extended Palette
- **Blue**: #0d6efd
- **Indigo**: #6610f2
- **Purple**: #6f42c1
- **Pink**: #d63384
- **Red**: #dc3545
- **Orange**: #fd7e14
- **Yellow**: #ffc107
- **Green**: #198754
- **Teal**: #20c997
- **Cyan**: #0dcaf0

## Typography
- **Font Family**: System font stack (system-ui, -apple-system, "Segoe UI", Roboto, etc.)
- **Body Color**: #212529
- **Body Background**: #fff
- **Custom Text Color**: #465665 (from Graham's site)

## Orange Accent Implementation

The orange accent color (#BB6D41 from Graham's site, plus Bootstrap's #fd7e14) is now prominently featured throughout:

### Visual Elements Using Orange:
1. **Main Title**: "Graham Hunt's Valencia Property Price Challenge" - prominent orange
2. **Subtitle**: Test your Valencia property knowledge subtitle - orange
3. **Icons**: All step and benefit icons (8 total)
4. **Titles**: Step and benefit card titles
5. **Section Headings**:
   - "How it works"
   - "This week's properties"
   - "How it helps you"
   - Hero kicker text
6. **Border Accents**:
   - Step cards have orange top borders
   - Benefit cards have orange left borders
   - Hero bullets have orange left border
7. **Hover Effects**:
   - Main title glows with orange on hover
   - Links transition to bright orange on hover
   - Logo gains orange glow filter on hover
   - Cards glow with orange shadow on hover
8. **Loading Bar**: Ajax loading bar is now orange

## Benefits

### Consistency
- All colors now match Graham Hunt's main website exactly
- Prominent use of signature orange accent color
- Uses industry-standard Bootstrap color naming conventions
- Consistent with common web development practices

### Maintainability
- Easy to update colors globally by changing CSS variables
- Clear separation between Bootstrap system colors and Valencia-specific mappings
- Reduced hardcoded color values throughout the codebase

### Professional Appearance
- Cohesive branding across all platforms
- Professional Bootstrap color palette
- Recognizable and trustworthy design system

## Testing Checklist
- [ ] Verify header uses solid Bootstrap dark background (#212529) - no transparency
- [ ] **Check main title "Graham Hunt's Valencia Property Price Challenge" is orange**
- [ ] **Verify subtitle text is orange**
- [ ] Confirm orange accent appears in all icons (steps and benefits - 8 total)
- [ ] Check all section headings use orange accent color (#BB6D41)
- [ ] Verify orange border accents on step cards (top), benefit cards (left), and hero bullets (left)
- [ ] **Test main title hover effect shows orange glow and brightening**
- [ ] Test hover effects show orange glow and transitions on cards
- [ ] Check hero card gradient uses Bootstrap colors
- [ ] Verify borders use Bootstrap gray-300 (#dee2e6)
- [ ] **Test logo blends well with dark header (transparent background, brightness filter)**
- [ ] **Test logo hover shows orange glow drop-shadow effect**
- [ ] Confirm ajax loading bar is orange
- [ ] Test link hover states transition to bright orange
- [ ] Test responsive behavior on mobile devices
- [ ] Verify no console errors
- [ ] Check color contrast for accessibility (orange #BB6D41 on white should meet WCAG AA standards)
- [ ] Test in different browsers (Chrome, Firefox, Safari)

## Notes
- The Valencia subdomain now fully incorporates Graham Hunt's Bootstrap-based color scheme
- **Orange accent color (#BB6D41 and #fd7e14) is prominently featured throughout the design**
- All custom blue colors (#1a3a5c, #0d1f33, #2c5f8d) have been replaced with Bootstrap equivalents
- Header transparency has been removed for a more solid, professional appearance
- The styling maintains backward compatibility through Valencia-specific variable mappings
- Hover effects and transitions create an engaging, interactive experience
- Future color updates can be made by modifying the Bootstrap variable values
