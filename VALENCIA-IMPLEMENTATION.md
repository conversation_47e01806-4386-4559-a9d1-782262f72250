# Valencia Subdomain Implementation

## Overview
Added "Valencia Properties" subdomain following the same pattern as <PERSON>. This was completed in approximately 5 minutes using the centralized configuration system.

## Changes Made

### 1. Configuration (`src/apps/hpg-main/router/subdomain-config.js`)
Added Valencia brand configuration:
```javascript
valencia: {
  name: 'valencia',
  displayName: 'Valencia Properties',
  homeUrl: 'https://valencia.housepriceguess.com/',
  serviceEmail: '<EMAIL>',
  whitelabelName: 'HousePriceGuess',
  logoUrl: '/icons/favicon-128x128.png',
  layoutComponent: 'src/concerns/valencia/layouts/ValenciaLayout.vue',
  homePageComponent: 'src/concerns/valencia/pages/ValenciaHomePage2025.vue',
  brandedGameSlug: 'valencia-house-prices-game',
  description: 'Valencia Properties branded experience'
}
```

### 2. Component Loader (`src/apps/hpg-main/router/subdomain-loader.js`)
Added Valencia to both import maps:
- `LAYOUT_IMPORTS.valencia` → ValenciaLayout.vue
- `HOMEPAGE_IMPORTS.valencia` → ValenciaHomePage2025.vue

### 3. Subdomain Utilities (`src/apps/hpg-main/router/subdomain-utils.js`)
Added helper function:
```javascript
export function isValenciaSubdomain(hostname) {
  return getSubdomainFromHostname(hostname) === 'valencia'
}
```

### 4. Components Created
- `src/concerns/valencia/layouts/ValenciaLayout.vue` - Layout with Valencia branding
- `src/concerns/valencia/pages/ValenciaHomePage2025.vue` - Homepage with Valencia branding

## Testing

### Local Development
1. Start dev server:
   ```bash
   ROOT_FOLDER_NAME=hpg-main quasar dev
   ```

2. Add to `/etc/hosts` (if not already present):
   ```
   127.0.0.1 valencia.lvh.me
   ```

3. Visit: `http://valencia.lvh.me:9000`

### Checklist
- [ ] Valencia layout displays with correct branding
- [ ] Valencia homepage shows "Valencia Properties Price Challenge"
- [ ] Game slug is `valencia-house-prices-game`
- [ ] No console errors
- [ ] Other subdomains (star, bvh, cameron, costa) still work
- [ ] Default subdomain still works

## Backend Setup Required

Before deploying to production, ensure:

1. **Database**: Create `valencia-house-prices-game` game entry
2. **Properties**: Add Valencia's property listings to the game
3. **DNS**: Configure `valencia.housepriceguess.com`
4. **SSL**: Ensure certificate covers valencia subdomain

## Customization Options

To customize Valencia further:

1. **Logo**: Update `logoUrl` in config to Valencia's actual logo
2. **Colors**: Modify CSS in ValenciaLayout.vue
3. **Content**: Edit homepage copy in ValenciaHomePage2025.vue
4. **Features**: Add Valencia-specific components/features

## Time to Implement
- Configuration: 1 minute
- Component creation: 2 minutes
- Testing: 2 minutes
- **Total: ~5 minutes**

This demonstrates the efficiency of the centralized configuration system!

## Notes
- Based on Cameron subdomain template
- Uses same branded game architecture
- Fully integrated with existing routing system
- No changes needed to core application code
