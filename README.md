# HomesToCompare (pwb-pro-quasar)

On 26 Nov 2024 this branch
https://github.com/etewiah/pwb-pro-fe-h2c
was created off
https://github.com/etewiah/pwb-pro-fe-admin

HomesToCompare is a platform that uses AI to analyze property listings, providing detailed insights and enabling users to compare properties for informed real estate decisions. The platform serves buyers, sellers, and real estate agents with tailored features.

## Project Overview

HomesToCompare empowers users by:

- Providing AI-generated property comparables
- Offering detailed property dossiers with room-by-room insights
- Enabling data-driven real estate decisions
- Preserving privacy while delivering valuable market insights

## Installation

```bash
# Install dependencies
yarn
# or
npm install
```

## Development

### Start development server

```bash
# Start the app in development mode (hot-code reloading, error reporting)
quasar dev
```

### Code Quality

```bash
# Lint the files
yarn lint
# or
npm run lint

# Format the files
yarn format
# or
npm run format
```

### Build for Production

```bash
# Build the app for production
quasar build
```

## Deployment

The deployment process uses Dokku:

```bash
# From project root
./deploy/dokkubuild/build-prod-pwbprofeh2c-2025-apr.sh
```

This compiles and copies the build to `/Users/<USER>/dev/sites-2024-may/htz-deploy-app/`.

## Project Structure

```
.
├── public/             # Static assets
├── src/
│   ├── apps/           # Application-specific code
│   ├── boot/           # Quasar boot files
│   ├── components/     # Reusable Vue components
│   ├── concerns/       # Feature-specific code organized by domain
│   │   ├── gpt-experiments/
│   │   ├── legacy-h2c/
│   │   ├── marketing/
│   │   └── user-dash/
│   ├── compose/        # Vue composition API utilities
│   ├── h2c/            # HomesToCompare core functionality
│   ├── layouts/        # Quasar layouts
│   └── pages/          # Vue pages
├── quasar.config.js    # Quasar framework configuration
└── package.json        # Project dependencies and scripts
```

## Technologies Used

- [Vue.js](https://vuejs.org/) - Frontend framework
- [Quasar Framework](https://quasar.dev/) - Vue-based UI framework
- [Vite](https://vitejs.dev/) - Build tool
- [Axios](https://axios-http.com/) - HTTP client
- [ApexCharts](https://apexcharts.com/) - Interactive charts
- [OpenLayers](https://openlayers.org/) - Map integration

## Multi-Subdomain Architecture

This application uses a **centralized configuration system** for multi-subdomain architecture, making it easy to serve different branded versions from the same codebase.

### Supported Subdomains

- **`star.housepriceguess.com`** - Star Team branded version
- **`bvh.housepriceguess.com`** - Buena Vista Homes branded version
- **`cameron.housepriceguess.com`** - Cameron Properties branded version ⭐ NEW
- **`costa.housepriceguess.com`** - Costa branded version
- **`housepriceguess.com`** - Default House Price Guess version

Each subdomain has:
- Custom layout and branding
- Specific homepage and components
- Unique API endpoints
- Tailored user experience

### Adding New Subdomains

As of October 2025, adding a new subdomain is simple:

1. Add entry to `src/apps/hpg-main/router/subdomain-config.js`
2. Create brand folder in `src/concerns/yourbrand/`
3. Add layout and homepage components
4. Test with `http://yourbrand.lvh.me:9000`

**See:** [Subdomain Configuration Guide](./docs/SUBDOMAIN-CONFIG-GUIDE.md) for step-by-step instructions.

### Local Development with Subdomains

Use `lvh.me` for local subdomain testing:

```bash
# Start development server
ROOT_FOLDER_NAME=hpg-main quasar dev

# Access different subdomains locally:
# http://star.lvh.me:9000
# http://bvh.lvh.me:9000
# http://cameron.lvh.me:9000     ← New!
# http://costa.lvh.me:9000
# http://lvh.me:9000
```

### Documentation

- 📖 [Subdomain Configuration Guide](./docs/SUBDOMAIN-CONFIG-GUIDE.md) - How to add/manage subdomains
- 📖 [Subdomain Architecture](./docs/SUBDOMAIN-ARCHITECTURE.md) - Detailed architecture
- 📖 [Subdomain Quick Reference](./docs/SUBDOMAIN-QUICK-REFERENCE.md) - Developer cheatsheet
- 📖 [Cameron Implementation](./CAMERON-IMPLEMENTATION.md) - Example of adding a new subdomain

## Key Features

### For Buyers

- Create dream home blueprints from actual sold properties
- Compare and assess potential options confidently
- Make data-driven decisions using AI-powered insights

### For Sellers

- Price properties strategically using comparable data
- Generate accurate synthetic comparables
- Preserve privacy while gaining market insights

### For Agents

- Offer clients precise property insights
- Save time with quick property data generation
- Attract more clients with data-driven proposals

## Browser Support

HomesToCompare supports all major modern browsers including:

- Chrome
- Firefox
- Safari
- Edge

## License

Proprietary - All rights reserved

## Contact

For more information, contact <EMAIL>
